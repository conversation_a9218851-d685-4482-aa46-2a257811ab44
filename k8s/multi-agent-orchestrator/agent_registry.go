package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// AgentRegistry manages the registration and discovery of AI agents
type AgentRegistry struct {
	agents          map[string]*Agent
	agentsByType    map[AgentType][]string
	agentsByCapability map[string][]string
	mu              sync.RWMutex
	healthChecker   *AgentHealthChecker
	storage         AgentStorage
}

// AgentStorage interface for persisting agent data
type AgentStorage interface {
	SaveAgent(agent Agent) error
	LoadAgent(agentID string) (*Agent, error)
	LoadAllAgents() ([]Agent, error)
	DeleteAgent(agentID string) error
	UpdateAgentStatus(agentID string, status AgentStatus) error
	UpdateAgentPerformance(agentID string, performance AgentPerformance) error
}

// MemoryAgentStorage is an in-memory implementation of AgentStorage
type MemoryAgentStorage struct {
	agents map[string]Agent
	mu     sync.RWMutex
}

// NewAgentRegistry creates a new agent registry
func NewAgentRegistry(storage AgentStorage) *AgentRegistry {
	registry := &AgentRegistry{
		agents:             make(map[string]*Agent),
		agentsByType:       make(map[AgentType][]string),
		agentsByCapability: make(map[string][]string),
		storage:            storage,
	}

	// Initialize health checker
	registry.healthChecker = NewAgentHealthChecker(registry)

	// Load existing agents from storage
	if err := registry.loadAgentsFromStorage(); err != nil {
		log.Printf("Warning: Failed to load agents from storage: %v", err)
	}

	// Start health checking
	go registry.healthChecker.Start()

	return registry
}

// RegisterAgent registers a new agent in the system
func (ar *AgentRegistry) RegisterAgent(agent Agent) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	// Validate agent
	if err := ar.validateAgent(agent); err != nil {
		return fmt.Errorf("agent validation failed: %w", err)
	}

	// Generate ID if not provided
	if agent.ID == "" {
		agent.ID = uuid.New().String()
	}

	// Set timestamps
	now := time.Now()
	agent.CreatedAt = now
	agent.UpdatedAt = now
	agent.LastSeen = now

	// Initialize performance metrics
	if agent.Performance.LastUpdated.IsZero() {
		agent.Performance.LastUpdated = now
	}

	// Store agent
	ar.agents[agent.ID] = &agent

	// Update indices
	ar.updateIndices(agent)

	// Persist to storage
	if err := ar.storage.SaveAgent(agent); err != nil {
		// Rollback in-memory changes
		delete(ar.agents, agent.ID)
		ar.removeFromIndices(agent)
		return fmt.Errorf("failed to persist agent: %w", err)
	}

	log.Printf("Agent registered successfully: %s (%s)", agent.Name, agent.ID)
	return nil
}

// GetAgent retrieves an agent by ID
func (ar *AgentRegistry) GetAgent(agentID string) (*Agent, error) {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}

	// Return a copy to prevent external modifications
	agentCopy := *agent
	return &agentCopy, nil
}

// ListAgents returns all registered agents
func (ar *AgentRegistry) ListAgents() []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agents := make([]Agent, 0, len(ar.agents))
	for _, agent := range ar.agents {
		agents = append(agents, *agent)
	}

	return agents
}

// ListAgentsByType returns agents of a specific type
func (ar *AgentRegistry) ListAgentsByType(agentType AgentType) []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agentIDs, exists := ar.agentsByType[agentType]
	if !exists {
		return []Agent{}
	}

	agents := make([]Agent, 0, len(agentIDs))
	for _, agentID := range agentIDs {
		if agent, exists := ar.agents[agentID]; exists {
			agents = append(agents, *agent)
		}
	}

	return agents
}

// FindAgentsByCapability finds agents that have a specific capability
func (ar *AgentRegistry) FindAgentsByCapability(capabilityID string) []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	agentIDs, exists := ar.agentsByCapability[capabilityID]
	if !exists {
		return []Agent{}
	}

	agents := make([]Agent, 0, len(agentIDs))
	for _, agentID := range agentIDs {
		if agent, exists := ar.agents[agentID]; exists {
			agents = append(agents, *agent)
		}
	}

	return agents
}

// UpdateAgentStatus updates the status of an agent
func (ar *AgentRegistry) UpdateAgentStatus(agentID string, status AgentStatus) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	agent.Status = status
	agent.UpdatedAt = time.Now()
	agent.LastSeen = time.Now()

	// Persist to storage
	if err := ar.storage.UpdateAgentStatus(agentID, status); err != nil {
		return fmt.Errorf("failed to persist status update: %w", err)
	}

	return nil
}

// UpdateAgentPerformance updates the performance metrics of an agent
func (ar *AgentRegistry) UpdateAgentPerformance(agentID string, performance AgentPerformance) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	performance.LastUpdated = time.Now()
	agent.Performance = performance
	agent.UpdatedAt = time.Now()

	// Persist to storage
	if err := ar.storage.UpdateAgentPerformance(agentID, performance); err != nil {
		return fmt.Errorf("failed to persist performance update: %w", err)
	}

	return nil
}

// UnregisterAgent removes an agent from the registry
func (ar *AgentRegistry) UnregisterAgent(agentID string) error {
	ar.mu.Lock()
	defer ar.mu.Unlock()

	agent, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	// Remove from indices
	ar.removeFromIndices(*agent)

	// Remove from memory
	delete(ar.agents, agentID)

	// Remove from storage
	if err := ar.storage.DeleteAgent(agentID); err != nil {
		return fmt.Errorf("failed to delete agent from storage: %w", err)
	}

	log.Printf("Agent unregistered: %s (%s)", agent.Name, agentID)
	return nil
}

// GetAvailableAgents returns agents that are currently available (idle status)
func (ar *AgentRegistry) GetAvailableAgents() []Agent {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	var availableAgents []Agent
	for _, agent := range ar.agents {
		if agent.Status == AgentStatusIdle {
			availableAgents = append(availableAgents, *agent)
		}
	}

	return availableAgents
}

// SelectBestAgent selects the best agent for a given task based on capabilities and performance
func (ar *AgentRegistry) SelectBestAgent(requiredCapabilities []string, preferences map[string]interface{}) (*Agent, error) {
	ar.mu.RLock()
	defer ar.mu.RUnlock()

	var candidates []*Agent

	// Find agents with required capabilities
	for _, agent := range ar.agents {
		if agent.Status != AgentStatusIdle {
			continue
		}

		hasAllCapabilities := true
		for _, reqCap := range requiredCapabilities {
			hasCapability := false
			for _, agentCap := range agent.Capabilities {
				if agentCap.ID == reqCap {
					hasCapability = true
					break
				}
			}
			if !hasCapability {
				hasAllCapabilities = false
				break
			}
		}

		if hasAllCapabilities {
			candidates = append(candidates, agent)
		}
	}

	if len(candidates) == 0 {
		return nil, fmt.Errorf("no available agents found with required capabilities")
	}

	// Select best candidate based on performance and preferences
	bestAgent := ar.selectBestCandidate(candidates, preferences)
	return bestAgent, nil
}

// Helper methods

func (ar *AgentRegistry) validateAgent(agent Agent) error {
	if agent.Name == "" {
		return fmt.Errorf("agent name is required")
	}
	if agent.Type == "" {
		return fmt.Errorf("agent type is required")
	}
	if agent.Endpoint == "" {
		return fmt.Errorf("agent endpoint is required")
	}
	return nil
}

func (ar *AgentRegistry) updateIndices(agent Agent) {
	// Update type index
	ar.agentsByType[agent.Type] = append(ar.agentsByType[agent.Type], agent.ID)

	// Update capability index
	for _, capability := range agent.Capabilities {
		ar.agentsByCapability[capability.ID] = append(ar.agentsByCapability[capability.ID], agent.ID)
	}
}

func (ar *AgentRegistry) removeFromIndices(agent Agent) {
	// Remove from type index
	if agentIDs, exists := ar.agentsByType[agent.Type]; exists {
		for i, id := range agentIDs {
			if id == agent.ID {
				ar.agentsByType[agent.Type] = append(agentIDs[:i], agentIDs[i+1:]...)
				break
			}
		}
	}

	// Remove from capability index
	for _, capability := range agent.Capabilities {
		if agentIDs, exists := ar.agentsByCapability[capability.ID]; exists {
			for i, id := range agentIDs {
				if id == agent.ID {
					ar.agentsByCapability[capability.ID] = append(agentIDs[:i], agentIDs[i+1:]...)
					break
				}
			}
		}
	}
}

func (ar *AgentRegistry) loadAgentsFromStorage() error {
	agents, err := ar.storage.LoadAllAgents()
	if err != nil {
		return err
	}

	for _, agent := range agents {
		ar.agents[agent.ID] = &agent
		ar.updateIndices(agent)
	}

	log.Printf("Loaded %d agents from storage", len(agents))
	return nil
}

func (ar *AgentRegistry) selectBestCandidate(candidates []*Agent, preferences map[string]interface{}) *Agent {
	if len(candidates) == 1 {
		return candidates[0]
	}

	// Default selection based on performance metrics
	bestAgent := candidates[0]
	bestScore := ar.calculateAgentScore(bestAgent, preferences)

	for _, candidate := range candidates[1:] {
		score := ar.calculateAgentScore(candidate, preferences)
		if score > bestScore {
			bestScore = score
			bestAgent = candidate
		}
	}

	return bestAgent
}

func (ar *AgentRegistry) calculateAgentScore(agent *Agent, preferences map[string]interface{}) float64 {
	// Base score from performance metrics
	score := agent.Performance.SuccessRate * 0.4 +
		agent.Performance.QualityScore * 0.3 +
		agent.Performance.CostEfficiency * 0.2 +
		(1.0 - agent.Performance.AverageLatency/1000.0) * 0.1

	// Apply preferences if provided
	if preferences != nil {
		if priorityType, exists := preferences["priority"]; exists {
			switch priorityType {
			case "cost":
				score = score*0.5 + agent.Performance.CostEfficiency*0.5
			case "quality":
				score = score*0.5 + agent.Performance.QualityScore*0.5
			case "speed":
				score = score*0.5 + (1.0-agent.Performance.AverageLatency/1000.0)*0.5
			}
		}
	}

	return score
}

// Memory storage implementation

func NewMemoryAgentStorage() *MemoryAgentStorage {
	return &MemoryAgentStorage{
		agents: make(map[string]Agent),
	}
}

func (mas *MemoryAgentStorage) SaveAgent(agent Agent) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()
	mas.agents[agent.ID] = agent
	return nil
}

func (mas *MemoryAgentStorage) LoadAgent(agentID string) (*Agent, error) {
	mas.mu.RLock()
	defer mas.mu.RUnlock()

	if agent, exists := mas.agents[agentID]; exists {
		return &agent, nil
	}
	return nil, fmt.Errorf("agent not found: %s", agentID)
}

func (mas *MemoryAgentStorage) LoadAllAgents() ([]Agent, error) {
	mas.mu.RLock()
	defer mas.mu.RUnlock()

	agents := make([]Agent, 0, len(mas.agents))
	for _, agent := range mas.agents {
		agents = append(agents, agent)
	}
	return agents, nil
}

func (mas *MemoryAgentStorage) DeleteAgent(agentID string) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()
	delete(mas.agents, agentID)
	return nil
}

func (mas *MemoryAgentStorage) UpdateAgentStatus(agentID string, status AgentStatus) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()

	if agent, exists := mas.agents[agentID]; exists {
		agent.Status = status
		agent.UpdatedAt = time.Now()
		mas.agents[agentID] = agent
		return nil
	}
	return fmt.Errorf("agent not found: %s", agentID)
}

func (mas *MemoryAgentStorage) UpdateAgentPerformance(agentID string, performance AgentPerformance) error {
	mas.mu.Lock()
	defer mas.mu.Unlock()

	if agent, exists := mas.agents[agentID]; exists {
		agent.Performance = performance
		agent.UpdatedAt = time.Now()
		mas.agents[agentID] = agent
		return nil
	}
	return fmt.Errorf("agent not found: %s", agentID)
}
