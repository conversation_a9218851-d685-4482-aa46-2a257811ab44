package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// WorkflowDesigner manages the creation and editing of multi-agent workflows
type WorkflowDesigner struct {
	workflows       map[string]*MultiAgentWorkflow
	templates       map[string]*WorkflowTemplate
	agentRegistry   *AgentRegistry
	storage         WorkflowStorage
	mu              sync.RWMutex
}

// WorkflowStorage interface for persisting workflow data
type WorkflowStorage interface {
	SaveWorkflow(workflow MultiAgentWorkflow) error
	LoadWorkflow(workflowID string) (*MultiAgentWorkflow, error)
	LoadAllWorkflows() ([]MultiAgentWorkflow, error)
	DeleteWorkflow(workflowID string) error
	SaveTemplate(template WorkflowTemplate) error
	LoadTemplate(templateID string) (*WorkflowTemplate, error)
	LoadAllTemplates() ([]WorkflowTemplate, error)
}

// WorkflowTemplate represents a reusable workflow pattern
type WorkflowTemplate struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	Category        string                 `json:"category"`
	RequiredAgents  []AgentRequirement     `json:"required_agents"`
	TaskTemplates   []TaskTemplate         `json:"task_templates"`
	Dependencies    []DependencyTemplate   `json:"dependencies"`
	Communication   []CommunicationTemplate `json:"communication"`
	EstimatedCost   float64                `json:"estimated_cost"`
	EstimatedTime   time.Duration          `json:"estimated_time"`
	Metadata        map[string]interface{} `json:"metadata"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// AgentRequirement specifies requirements for agents in a workflow
type AgentRequirement struct {
	Role         string   `json:"role"`
	Type         AgentType `json:"type"`
	Capabilities []string `json:"capabilities"`
	MinQuality   float64  `json:"min_quality"`
	MaxCost      float64  `json:"max_cost"`
	Optional     bool     `json:"optional"`
}

// TaskTemplate represents a template for workflow tasks
type TaskTemplate struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	AgentRole       string                 `json:"agent_role"`
	Type            string                 `json:"type"`
	Parameters      map[string]interface{} `json:"parameters"`
	EstimatedCost   float64                `json:"estimated_cost"`
	EstimatedTime   time.Duration          `json:"estimated_time"`
	Priority        int                    `json:"priority"`
}

// DependencyTemplate represents a template for task dependencies
type DependencyTemplate struct {
	FromTaskID  string `json:"from_task_id"`
	ToTaskID    string `json:"to_task_id"`
	Type        string `json:"type"`
	Description string `json:"description"`
}

// CommunicationTemplate represents a template for agent communication
type CommunicationTemplate struct {
	FromAgentRole string                 `json:"from_agent_role"`
	ToAgentRole   string                 `json:"to_agent_role"`
	MessageType   string                 `json:"message_type"`
	Trigger       string                 `json:"trigger"`
	Parameters    map[string]interface{} `json:"parameters"`
	Description   string                 `json:"description"`
}

// WorkflowCreationRequest represents a request to create a new workflow
type WorkflowCreationRequest struct {
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	GoalID          string                 `json:"goal_id,omitempty"`
	TemplateID      string                 `json:"template_id,omitempty"`
	AgentAssignments map[string]string     `json:"agent_assignments,omitempty"`
	CustomTasks     []WorkflowTask         `json:"custom_tasks,omitempty"`
	Preferences     map[string]interface{} `json:"preferences,omitempty"`
}

// NewWorkflowDesigner creates a new workflow designer
func NewWorkflowDesigner(agentRegistry *AgentRegistry, storage WorkflowStorage) *WorkflowDesigner {
	designer := &WorkflowDesigner{
		workflows:     make(map[string]*MultiAgentWorkflow),
		templates:     make(map[string]*WorkflowTemplate),
		agentRegistry: agentRegistry,
		storage:       storage,
	}

	// Load existing workflows and templates
	if err := designer.loadFromStorage(); err != nil {
		log.Printf("Warning: Failed to load workflows from storage: %v", err)
	}

	// Initialize default templates
	designer.initializeDefaultTemplates()

	return designer
}

// CreateWorkflow creates a new multi-agent workflow
func (wd *WorkflowDesigner) CreateWorkflow(request WorkflowCreationRequest) (*MultiAgentWorkflow, error) {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	// Validate request
	if err := wd.validateCreationRequest(request); err != nil {
		return nil, fmt.Errorf("invalid workflow request: %w", err)
	}

	// Create workflow structure
	workflow := &MultiAgentWorkflow{
		ID:          uuid.New().String(),
		Name:        request.Name,
		Description: request.Description,
		GoalID:      request.GoalID,
		Status:      WorkflowStatusDraft,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Metadata:    make(map[string]interface{}),
	}

	var err error

	// Build workflow from template or custom specification
	if request.TemplateID != "" {
		err = wd.buildFromTemplate(workflow, request)
	} else {
		err = wd.buildCustomWorkflow(workflow, request)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to build workflow: %w", err)
	}

	// Assign agents to tasks
	if err := wd.assignAgents(workflow, request.AgentAssignments, request.Preferences); err != nil {
		return nil, fmt.Errorf("failed to assign agents: %w", err)
	}

	// Calculate estimates
	wd.calculateEstimates(workflow)

	// Store workflow
	wd.workflows[workflow.ID] = workflow
	if err := wd.storage.SaveWorkflow(*workflow); err != nil {
		delete(wd.workflows, workflow.ID)
		return nil, fmt.Errorf("failed to persist workflow: %w", err)
	}

	log.Printf("Created workflow: %s (%s)", workflow.Name, workflow.ID)
	return workflow, nil
}

// GetWorkflow retrieves a workflow by ID
func (wd *WorkflowDesigner) GetWorkflow(workflowID string) (*MultiAgentWorkflow, error) {
	wd.mu.RLock()
	defer wd.mu.RUnlock()

	workflow, exists := wd.workflows[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow not found: %s", workflowID)
	}

	// Return a copy
	workflowCopy := *workflow
	return &workflowCopy, nil
}

// ListWorkflows returns all workflows
func (wd *WorkflowDesigner) ListWorkflows() []MultiAgentWorkflow {
	wd.mu.RLock()
	defer wd.mu.RUnlock()

	workflows := make([]MultiAgentWorkflow, 0, len(wd.workflows))
	for _, workflow := range wd.workflows {
		workflows = append(workflows, *workflow)
	}

	return workflows
}

// UpdateWorkflow updates an existing workflow
func (wd *WorkflowDesigner) UpdateWorkflow(workflowID string, updates map[string]interface{}) error {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	workflow, exists := wd.workflows[workflowID]
	if !exists {
		return fmt.Errorf("workflow not found: %s", workflowID)
	}

	// Only allow updates to draft workflows
	if workflow.Status != WorkflowStatusDraft {
		return fmt.Errorf("cannot update workflow in status: %s", workflow.Status)
	}

	// Apply updates
	if name, ok := updates["name"].(string); ok {
		workflow.Name = name
	}
	if description, ok := updates["description"].(string); ok {
		workflow.Description = description
	}

	workflow.UpdatedAt = time.Now()

	// Persist changes
	if err := wd.storage.SaveWorkflow(*workflow); err != nil {
		return fmt.Errorf("failed to persist workflow updates: %w", err)
	}

	return nil
}

// DeleteWorkflow removes a workflow
func (wd *WorkflowDesigner) DeleteWorkflow(workflowID string) error {
	wd.mu.Lock()
	defer wd.mu.Unlock()

	workflow, exists := wd.workflows[workflowID]
	if !exists {
		return fmt.Errorf("workflow not found: %s", workflowID)
	}

	// Only allow deletion of draft or completed workflows
	if workflow.Status == WorkflowStatusExecuting {
		return fmt.Errorf("cannot delete executing workflow")
	}

	delete(wd.workflows, workflowID)
	if err := wd.storage.DeleteWorkflow(workflowID); err != nil {
		return fmt.Errorf("failed to delete workflow from storage: %w", err)
	}

	log.Printf("Deleted workflow: %s (%s)", workflow.Name, workflowID)
	return nil
}

// ValidateWorkflow validates a workflow for execution
func (wd *WorkflowDesigner) ValidateWorkflow(workflowID string) ([]string, error) {
	wd.mu.RLock()
	defer wd.mu.RUnlock()

	workflow, exists := wd.workflows[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow not found: %s", workflowID)
	}

	var issues []string

	// Check if all tasks have assigned agents
	for _, task := range workflow.Tasks {
		if task.AssignedAgentID == "" {
			issues = append(issues, fmt.Sprintf("Task '%s' has no assigned agent", task.Name))
			continue
		}

		// Check if assigned agent exists and is available
		agent, err := wd.agentRegistry.GetAgent(task.AssignedAgentID)
		if err != nil {
			issues = append(issues, fmt.Sprintf("Task '%s' assigned to non-existent agent: %s", task.Name, task.AssignedAgentID))
			continue
		}

		if agent.Status != AgentStatusIdle {
			issues = append(issues, fmt.Sprintf("Task '%s' assigned to unavailable agent: %s (status: %s)", task.Name, agent.Name, agent.Status))
		}
	}

	// Check for circular dependencies
	if wd.hasCircularDependencies(workflow) {
		issues = append(issues, "Workflow contains circular dependencies")
	}

	// Check if workflow has at least one task
	if len(workflow.Tasks) == 0 {
		issues = append(issues, "Workflow has no tasks")
	}

	return issues, nil
}

// GetTemplates returns all available workflow templates
func (wd *WorkflowDesigner) GetTemplates() []WorkflowTemplate {
	wd.mu.RLock()
	defer wd.mu.RUnlock()

	templates := make([]WorkflowTemplate, 0, len(wd.templates))
	for _, template := range wd.templates {
		templates = append(templates, *template)
	}

	return templates
}

// Helper methods

func (wd *WorkflowDesigner) validateCreationRequest(request WorkflowCreationRequest) error {
	if request.Name == "" {
		return fmt.Errorf("workflow name is required")
	}
	if request.TemplateID == "" && len(request.CustomTasks) == 0 {
		return fmt.Errorf("either template_id or custom_tasks must be provided")
	}
	return nil
}

func (wd *WorkflowDesigner) buildFromTemplate(workflow *MultiAgentWorkflow, request WorkflowCreationRequest) error {
	template, exists := wd.templates[request.TemplateID]
	if !exists {
		return fmt.Errorf("template not found: %s", request.TemplateID)
	}

	// Build tasks from template
	for _, taskTemplate := range template.TaskTemplates {
		task := WorkflowTask{
			ID:            uuid.New().String(),
			WorkflowID:    workflow.ID,
			Name:          taskTemplate.Name,
			Description:   taskTemplate.Description,
			Type:          taskTemplate.Type,
			Parameters:    taskTemplate.Parameters,
			Status:        "pending",
			Priority:      taskTemplate.Priority,
			EstimatedCost: taskTemplate.EstimatedCost,
			EstimatedTime: taskTemplate.EstimatedTime,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		workflow.Tasks = append(workflow.Tasks, task)
	}

	// Build dependencies from template
	taskIDMap := make(map[string]string) // template task ID -> actual task ID
	for i, task := range workflow.Tasks {
		taskIDMap[template.TaskTemplates[i].ID] = task.ID
	}

	for _, depTemplate := range template.Dependencies {
		dependency := WorkflowDependency{
			ID:          uuid.New().String(),
			WorkflowID:  workflow.ID,
			FromTaskID:  taskIDMap[depTemplate.FromTaskID],
			ToTaskID:    taskIDMap[depTemplate.ToTaskID],
			Type:        depTemplate.Type,
			Description: depTemplate.Description,
		}
		workflow.Dependencies = append(workflow.Dependencies, dependency)
	}

	workflow.EstimatedCost = template.EstimatedCost
	workflow.EstimatedTime = template.EstimatedTime

	return nil
}

func (wd *WorkflowDesigner) buildCustomWorkflow(workflow *MultiAgentWorkflow, request WorkflowCreationRequest) error {
	// Use custom tasks provided in the request
	for _, customTask := range request.CustomTasks {
		customTask.ID = uuid.New().String()
		customTask.WorkflowID = workflow.ID
		customTask.CreatedAt = time.Now()
		customTask.UpdatedAt = time.Now()
		workflow.Tasks = append(workflow.Tasks, customTask)
	}

	return nil
}

func (wd *WorkflowDesigner) assignAgents(workflow *MultiAgentWorkflow, assignments map[string]string, preferences map[string]interface{}) error {
	for i := range workflow.Tasks {
		task := &workflow.Tasks[i]

		// Use explicit assignment if provided
		if agentID, exists := assignments[task.ID]; exists {
			agent, err := wd.agentRegistry.GetAgent(agentID)
			if err != nil {
				return fmt.Errorf("assigned agent not found: %s", agentID)
			}
			if agent.Status != AgentStatusIdle {
				return fmt.Errorf("assigned agent is not available: %s", agentID)
			}
			task.AssignedAgentID = agentID
			continue
		}

		// Auto-assign based on task requirements
		requiredCapabilities := []string{task.Type}
		agent, err := wd.agentRegistry.SelectBestAgent(requiredCapabilities, preferences)
		if err != nil {
			return fmt.Errorf("failed to auto-assign agent for task '%s': %w", task.Name, err)
		}
		task.AssignedAgentID = agent.ID
	}

	// Update agents list
	agentSet := make(map[string]bool)
	for _, task := range workflow.Tasks {
		agentSet[task.AssignedAgentID] = true
	}
	workflow.Agents = make([]string, 0, len(agentSet))
	for agentID := range agentSet {
		workflow.Agents = append(workflow.Agents, agentID)
	}

	return nil
}

func (wd *WorkflowDesigner) calculateEstimates(workflow *MultiAgentWorkflow) {
	var totalCost float64
	var maxTime time.Duration

	for _, task := range workflow.Tasks {
		totalCost += task.EstimatedCost
		if task.EstimatedTime > maxTime {
			maxTime = task.EstimatedTime
		}
	}

	workflow.EstimatedCost = totalCost
	workflow.EstimatedTime = maxTime
}

func (wd *WorkflowDesigner) hasCircularDependencies(workflow *MultiAgentWorkflow) bool {
	// Build adjacency list
	graph := make(map[string][]string)
	for _, dep := range workflow.Dependencies {
		graph[dep.FromTaskID] = append(graph[dep.FromTaskID], dep.ToTaskID)
	}

	// DFS to detect cycles
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	var hasCycle func(string) bool
	hasCycle = func(taskID string) bool {
		visited[taskID] = true
		recStack[taskID] = true

		for _, neighbor := range graph[taskID] {
			if !visited[neighbor] && hasCycle(neighbor) {
				return true
			} else if recStack[neighbor] {
				return true
			}
		}

		recStack[taskID] = false
		return false
	}

	for _, task := range workflow.Tasks {
		if !visited[task.ID] && hasCycle(task.ID) {
			return true
		}
	}

	return false
}

func (wd *WorkflowDesigner) loadFromStorage() error {
	// Load workflows
	workflows, err := wd.storage.LoadAllWorkflows()
	if err != nil {
		return fmt.Errorf("failed to load workflows: %w", err)
	}

	for _, workflow := range workflows {
		wd.workflows[workflow.ID] = &workflow
	}

	// Load templates
	templates, err := wd.storage.LoadAllTemplates()
	if err != nil {
		return fmt.Errorf("failed to load templates: %w", err)
	}

	for _, template := range templates {
		wd.templates[template.ID] = &template
	}

	log.Printf("Loaded %d workflows and %d templates from storage", len(workflows), len(templates))
	return nil
}

func (wd *WorkflowDesigner) initializeDefaultTemplates() {
	// Add some default workflow templates
	templates := []WorkflowTemplate{
		{
			ID:          "data-analysis-pipeline",
			Name:        "Data Analysis Pipeline",
			Description: "A workflow for comprehensive data analysis involving data collection, processing, analysis, and reporting",
			Category:    "analytics",
			RequiredAgents: []AgentRequirement{
				{Role: "collector", Type: AgentTypeDataAnalyst, Capabilities: []string{"data_collection"}, MinQuality: 0.8},
				{Role: "processor", Type: AgentTypeDataAnalyst, Capabilities: []string{"data_processing"}, MinQuality: 0.8},
				{Role: "analyst", Type: AgentTypeDataAnalyst, Capabilities: []string{"statistical_analysis"}, MinQuality: 0.9},
				{Role: "reporter", Type: AgentTypeContentWriter, Capabilities: []string{"report_generation"}, MinQuality: 0.8},
			},
			EstimatedCost: 5.0,
			EstimatedTime: 30 * time.Minute,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
		{
			ID:          "content-creation-workflow",
			Name:        "Content Creation Workflow",
			Description: "A workflow for creating high-quality content involving research, writing, and review",
			Category:    "content",
			RequiredAgents: []AgentRequirement{
				{Role: "researcher", Type: AgentTypeResearcher, Capabilities: []string{"web_research"}, MinQuality: 0.8},
				{Role: "writer", Type: AgentTypeContentWriter, Capabilities: []string{"content_writing"}, MinQuality: 0.9},
				{Role: "reviewer", Type: AgentTypeValidator, Capabilities: []string{"content_review"}, MinQuality: 0.8},
			},
			EstimatedCost: 3.0,
			EstimatedTime: 20 * time.Minute,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		},
	}

	for _, template := range templates {
		wd.templates[template.ID] = &template
		if err := wd.storage.SaveTemplate(template); err != nil {
			log.Printf("Warning: Failed to save default template %s: %v", template.ID, err)
		}
	}

	log.Printf("Initialized %d default workflow templates", len(templates))
}
