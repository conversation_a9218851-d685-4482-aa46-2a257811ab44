package main

import (
	"fmt"
	"log"
	"math"
	"sort"
	"time"
)

// AgentSelector provides advanced agent selection algorithms
type AgentSelector struct {
	registry         *AgentRegistry
	performanceCache map[string]*AgentPerformanceHistory
	workloadTracker  *WorkloadTracker
	mlModel          *AgentSelectionModel
}

// AgentPerformanceHistory tracks detailed performance metrics over time
type AgentPerformanceHistory struct {
	AgentID           string                   `json:"agent_id"`
	PerformancePoints []PerformanceDataPoint   `json:"performance_points"`
	CapabilityScores  map[string]float64       `json:"capability_scores"`
	WorkloadHistory   []WorkloadDataPoint      `json:"workload_history"`
	CollaborationData []CollaborationDataPoint `json:"collaboration_data"`
	LastUpdated       time.Time                `json:"last_updated"`
}

// PerformanceDataPoint represents a single performance measurement
type PerformanceDataPoint struct {
	Timestamp      time.Time              `json:"timestamp"`
	TaskType       string                 `json:"task_type"`
	SuccessRate    float64                `json:"success_rate"`
	Latency        float64                `json:"latency"`
	QualityScore   float64                `json:"quality_score"`
	CostEfficiency float64                `json:"cost_efficiency"`
	Context        map[string]interface{} `json:"context"`
}

// WorkloadDataPoint tracks agent workload over time
type WorkloadDataPoint struct {
	Timestamp       time.Time `json:"timestamp"`
	ActiveTasks     int       `json:"active_tasks"`
	QueuedTasks     int       `json:"queued_tasks"`
	UtilizationRate float64   `json:"utilization_rate"`
	StressLevel     float64   `json:"stress_level"`
}

// CollaborationDataPoint tracks collaboration effectiveness
type CollaborationDataPoint struct {
	Timestamp        time.Time `json:"timestamp"`
	PartnerAgentID   string    `json:"partner_agent_id"`
	WorkflowID       string    `json:"workflow_id"`
	SynergyScore     float64   `json:"synergy_score"`
	CommunicationEff float64   `json:"communication_efficiency"`
	ConflictRate     float64   `json:"conflict_rate"`
}

// WorkloadTracker monitors current workload across all agents
type WorkloadTracker struct {
	agentWorkloads map[string]*CurrentWorkload
	globalMetrics  *GlobalWorkloadMetrics
}

// CurrentWorkload represents current agent workload
type CurrentWorkload struct {
	AgentID       string    `json:"agent_id"`
	ActiveTasks   []string  `json:"active_tasks"`
	QueuedTasks   []string  `json:"queued_tasks"`
	EstimatedLoad float64   `json:"estimated_load"`
	CapacityLimit float64   `json:"capacity_limit"`
	LastUpdated   time.Time `json:"last_updated"`
}

// GlobalWorkloadMetrics tracks system-wide workload distribution
type GlobalWorkloadMetrics struct {
	TotalActiveAgents   int      `json:"total_active_agents"`
	AverageUtilization  float64  `json:"average_utilization"`
	LoadBalanceScore    float64  `json:"load_balance_score"`
	BottleneckAgents    []string `json:"bottleneck_agents"`
	UnderutilizedAgents []string `json:"underutilized_agents"`
}

// AgentSelectionModel provides ML-driven agent selection
type AgentSelectionModel struct {
	weights          map[string]float64
	learningRate     float64
	decayFactor      float64
	confidenceThresh float64
}

// AgentSelectionRequest represents a request for agent selection
type AgentSelectionRequest struct {
	TaskType             string                 `json:"task_type"`
	RequiredCapabilities []string               `json:"required_capabilities"`
	QualityRequirement   float64                `json:"quality_requirement"`
	LatencyRequirement   time.Duration          `json:"latency_requirement"`
	CostConstraint       float64                `json:"cost_constraint"`
	WorkflowContext      map[string]interface{} `json:"workflow_context"`
	CollaborationNeeds   []string               `json:"collaboration_needs"`
	Priority             int                    `json:"priority"`
}

// AgentSelectionResult contains the selection result with confidence scores
type AgentSelectionResult struct {
	SelectedAgent     *Agent           `json:"selected_agent"`
	ConfidenceScore   float64          `json:"confidence_score"`
	AlternativeAgents []AgentCandidate `json:"alternative_agents"`
	SelectionReason   string           `json:"selection_reason"`
	RiskFactors       []string         `json:"risk_factors"`
	Recommendations   []string         `json:"recommendations"`
}

// AgentCandidate represents a potential agent selection
type AgentCandidate struct {
	Agent           *Agent  `json:"agent"`
	Score           float64 `json:"score"`
	MatchConfidence float64 `json:"match_confidence"`
	Reasoning       string  `json:"reasoning"`
}

// NewAgentSelector creates a new advanced agent selector
func NewAgentSelector(registry *AgentRegistry) *AgentSelector {
	return &AgentSelector{
		registry:         registry,
		performanceCache: make(map[string]*AgentPerformanceHistory),
		workloadTracker:  NewWorkloadTracker(),
		mlModel:          NewAgentSelectionModel(),
	}
}

// SelectOptimalAgent selects the best agent using advanced algorithms
func (as *AgentSelector) SelectOptimalAgent(request AgentSelectionRequest) (*AgentSelectionResult, error) {
	// Get available agents
	availableAgents := as.registry.GetAvailableAgents()
	if len(availableAgents) == 0 {
		return nil, fmt.Errorf("no available agents")
	}

	// Filter agents by capabilities
	candidates := as.filterByCapabilities(availableAgents, request.RequiredCapabilities)
	if len(candidates) == 0 {
		return nil, fmt.Errorf("no agents found with required capabilities")
	}

	// Score each candidate
	scoredCandidates := make([]AgentCandidate, 0, len(candidates))
	for _, agent := range candidates {
		score, confidence, reasoning := as.scoreAgent(agent, request)

		scoredCandidates = append(scoredCandidates, AgentCandidate{
			Agent:           &agent,
			Score:           score,
			MatchConfidence: confidence,
			Reasoning:       reasoning,
		})
	}

	// Sort by score
	sort.Slice(scoredCandidates, func(i, j int) bool {
		return scoredCandidates[i].Score > scoredCandidates[j].Score
	})

	if len(scoredCandidates) == 0 {
		return nil, fmt.Errorf("no suitable agents found")
	}

	// Select best candidate
	bestCandidate := scoredCandidates[0]

	// Assess risks and provide recommendations
	riskFactors := as.assessRisks(bestCandidate.Agent, request)
	recommendations := as.generateRecommendations(bestCandidate.Agent, request, scoredCandidates)

	result := &AgentSelectionResult{
		SelectedAgent:     bestCandidate.Agent,
		ConfidenceScore:   bestCandidate.MatchConfidence,
		AlternativeAgents: scoredCandidates[1:min(len(scoredCandidates), 4)], // Top 3 alternatives
		SelectionReason:   bestCandidate.Reasoning,
		RiskFactors:       riskFactors,
		Recommendations:   recommendations,
	}

	// Update ML model with selection
	as.mlModel.recordSelection(request, bestCandidate)

	log.Printf("Selected agent %s with confidence %.2f for task type %s",
		bestCandidate.Agent.Name, bestCandidate.MatchConfidence, request.TaskType)

	return result, nil
}

// scoreAgent calculates a comprehensive score for an agent
func (as *AgentSelector) scoreAgent(agent Agent, request AgentSelectionRequest) (float64, float64, string) {
	var totalScore float64
	var reasoning []string

	// 1. Capability match score (30%)
	capabilityScore := as.calculateCapabilityScore(agent, request)
	totalScore += capabilityScore * 0.30
	reasoning = append(reasoning, fmt.Sprintf("Capability match: %.2f", capabilityScore))

	// 2. Performance history score (25%)
	performanceScore := as.calculatePerformanceScore(agent, request)
	totalScore += performanceScore * 0.25
	reasoning = append(reasoning, fmt.Sprintf("Performance history: %.2f", performanceScore))

	// 3. Workload balance score (20%)
	workloadScore := as.calculateWorkloadScore(agent, request)
	totalScore += workloadScore * 0.20
	reasoning = append(reasoning, fmt.Sprintf("Workload balance: %.2f", workloadScore))

	// 4. Cost efficiency score (15%)
	costScore := as.calculateCostScore(agent, request)
	totalScore += costScore * 0.15
	reasoning = append(reasoning, fmt.Sprintf("Cost efficiency: %.2f", costScore))

	// 5. Collaboration compatibility score (10%)
	collaborationScore := as.calculateCollaborationScore(agent, request)
	totalScore += collaborationScore * 0.10
	reasoning = append(reasoning, fmt.Sprintf("Collaboration fit: %.2f", collaborationScore))

	// Calculate confidence based on data quality and consistency
	confidence := as.calculateConfidence(agent, request)

	reasoningText := fmt.Sprintf("Score breakdown: %s",
		fmt.Sprintf("[%s]", joinStrings(reasoning, ", ")))

	return totalScore, confidence, reasoningText
}

// calculateCapabilityScore evaluates how well agent capabilities match requirements
func (as *AgentSelector) calculateCapabilityScore(agent Agent, request AgentSelectionRequest) float64 {
	if len(request.RequiredCapabilities) == 0 {
		return 1.0
	}

	var matchScore float64
	var totalWeight float64

	for _, reqCap := range request.RequiredCapabilities {
		found := false
		for _, agentCap := range agent.Capabilities {
			if agentCap.ID == reqCap {
				// Weight by capability quality and relevance
				weight := agentCap.Quality * agentCap.Speed
				matchScore += weight
				totalWeight += 1.0
				found = true
				break
			}
		}
		if !found {
			totalWeight += 1.0 // Penalty for missing capability
		}
	}

	if totalWeight == 0 {
		return 0
	}

	return matchScore / totalWeight
}

// calculatePerformanceScore evaluates agent's historical performance
func (as *AgentSelector) calculatePerformanceScore(agent Agent, request AgentSelectionRequest) float64 {
	history, exists := as.performanceCache[agent.ID]
	if !exists || len(history.PerformancePoints) == 0 {
		// Use current performance metrics as fallback
		return (agent.Performance.SuccessRate + agent.Performance.QualityScore + agent.Performance.CostEfficiency) / 3.0
	}

	// Calculate weighted average of recent performance
	var weightedScore float64
	var totalWeight float64
	now := time.Now()

	for _, point := range history.PerformancePoints {
		// Weight recent performance more heavily
		age := now.Sub(point.Timestamp).Hours()
		weight := math.Exp(-age / 168.0) // Decay over 1 week

		if request.TaskType != "" && point.TaskType == request.TaskType {
			weight *= 1.5 // Boost for same task type
		}

		score := (point.SuccessRate + point.QualityScore + point.CostEfficiency) / 3.0
		weightedScore += score * weight
		totalWeight += weight
	}

	if totalWeight == 0 {
		return 0.5 // Neutral score
	}

	return weightedScore / totalWeight
}

// calculateWorkloadScore evaluates current workload and capacity
func (as *AgentSelector) calculateWorkloadScore(agent Agent, request AgentSelectionRequest) float64 {
	workload := as.workloadTracker.GetAgentWorkload(agent.ID)
	if workload == nil {
		return 1.0 // Assume available if no workload data
	}

	utilization := workload.EstimatedLoad / workload.CapacityLimit

	// Optimal utilization is around 70-80%
	if utilization < 0.7 {
		return 1.0 - (0.7-utilization)*0.5 // Slight penalty for underutilization
	} else if utilization < 0.8 {
		return 1.0 // Optimal range
	} else if utilization < 0.95 {
		return 1.0 - (utilization-0.8)*2.0 // Penalty for high utilization
	} else {
		return 0.1 // Heavy penalty for overutilization
	}
}

// calculateCostScore evaluates cost efficiency
func (as *AgentSelector) calculateCostScore(agent Agent, request AgentSelectionRequest) float64 {
	if request.CostConstraint <= 0 {
		return 1.0 // No cost constraint
	}

	// Estimate cost based on agent capabilities and historical data
	estimatedCost := as.estimateTaskCost(agent, request)

	if estimatedCost <= request.CostConstraint {
		// Reward agents that are well under budget
		efficiency := 1.0 - (estimatedCost / request.CostConstraint)
		return 0.5 + efficiency*0.5
	} else {
		// Penalty for exceeding budget
		overage := estimatedCost / request.CostConstraint
		return math.Max(0.0, 1.0-overage)
	}
}

// calculateCollaborationScore evaluates collaboration compatibility
func (as *AgentSelector) calculateCollaborationScore(agent Agent, request AgentSelectionRequest) float64 {
	if len(request.CollaborationNeeds) == 0 {
		return 1.0 // No collaboration requirements
	}

	history, exists := as.performanceCache[agent.ID]
	if !exists || len(history.CollaborationData) == 0 {
		return 0.7 // Neutral score for unknown collaboration ability
	}

	// Calculate average collaboration effectiveness
	var totalSynergy float64
	var totalComm float64
	var count float64

	for _, collab := range history.CollaborationData {
		totalSynergy += collab.SynergyScore
		totalComm += collab.CommunicationEff
		count++
	}

	if count == 0 {
		return 0.7
	}

	avgSynergy := totalSynergy / count
	avgComm := totalComm / count

	return (avgSynergy + avgComm) / 2.0
}

// calculateConfidence assesses confidence in the selection
func (as *AgentSelector) calculateConfidence(agent Agent, request AgentSelectionRequest) float64 {
	var factors []float64

	// Data availability factor
	history, exists := as.performanceCache[agent.ID]
	if exists && len(history.PerformancePoints) > 10 {
		factors = append(factors, 1.0)
	} else if exists && len(history.PerformancePoints) > 0 {
		factors = append(factors, 0.7)
	} else {
		factors = append(factors, 0.3)
	}

	// Task type match factor
	if request.TaskType != "" {
		hasExperience := false
		if exists {
			for _, point := range history.PerformancePoints {
				if point.TaskType == request.TaskType {
					hasExperience = true
					break
				}
			}
		}
		if hasExperience {
			factors = append(factors, 1.0)
		} else {
			factors = append(factors, 0.5)
		}
	}

	// Capability match factor
	capabilityMatch := as.calculateCapabilityScore(agent, request)
	factors = append(factors, capabilityMatch)

	// Calculate average confidence
	var sum float64
	for _, factor := range factors {
		sum += factor
	}

	return sum / float64(len(factors))
}

// Helper functions

func (as *AgentSelector) filterByCapabilities(agents []Agent, requiredCapabilities []string) []Agent {
	if len(requiredCapabilities) == 0 {
		return agents
	}

	var filtered []Agent
	for _, agent := range agents {
		hasAllCapabilities := true
		for _, reqCap := range requiredCapabilities {
			hasCapability := false
			for _, agentCap := range agent.Capabilities {
				if agentCap.ID == reqCap {
					hasCapability = true
					break
				}
			}
			if !hasCapability {
				hasAllCapabilities = false
				break
			}
		}
		if hasAllCapabilities {
			filtered = append(filtered, agent)
		}
	}

	return filtered
}

func (as *AgentSelector) estimateTaskCost(agent Agent, request AgentSelectionRequest) float64 {
	// Simple cost estimation based on agent capabilities
	var totalCost float64
	for _, reqCap := range request.RequiredCapabilities {
		for _, agentCap := range agent.Capabilities {
			if agentCap.ID == reqCap {
				totalCost += agentCap.Cost
				break
			}
		}
	}
	return totalCost
}

func (as *AgentSelector) assessRisks(agent *Agent, request AgentSelectionRequest) []string {
	var risks []string

	// Check workload risk
	workload := as.workloadTracker.GetAgentWorkload(agent.ID)
	if workload != nil && workload.EstimatedLoad/workload.CapacityLimit > 0.9 {
		risks = append(risks, "High workload may impact performance")
	}

	// Check performance consistency
	history, exists := as.performanceCache[agent.ID]
	if exists && len(history.PerformancePoints) > 5 {
		var variance float64
		var mean float64
		for _, point := range history.PerformancePoints {
			score := (point.SuccessRate + point.QualityScore) / 2.0
			mean += score
		}
		mean /= float64(len(history.PerformancePoints))

		for _, point := range history.PerformancePoints {
			score := (point.SuccessRate + point.QualityScore) / 2.0
			variance += math.Pow(score-mean, 2)
		}
		variance /= float64(len(history.PerformancePoints))

		if variance > 0.1 {
			risks = append(risks, "Inconsistent performance history")
		}
	}

	return risks
}

func (as *AgentSelector) generateRecommendations(agent *Agent, request AgentSelectionRequest, candidates []AgentCandidate) []string {
	var recommendations []string

	// Recommend monitoring if confidence is low
	if len(candidates) > 0 && candidates[0].MatchConfidence < 0.7 {
		recommendations = append(recommendations, "Monitor task execution closely due to lower confidence")
	}

	// Recommend alternatives if scores are close
	if len(candidates) > 1 && candidates[1].Score > candidates[0].Score*0.9 {
		recommendations = append(recommendations, fmt.Sprintf("Consider %s as alternative (score: %.2f)",
			candidates[1].Agent.Name, candidates[1].Score))
	}

	return recommendations
}

func joinStrings(strs []string, sep string) string {
	if len(strs) == 0 {
		return ""
	}
	result := strs[0]
	for i := 1; i < len(strs); i++ {
		result += sep + strs[i]
	}
	return result
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// NewWorkloadTracker creates a new workload tracker
func NewWorkloadTracker() *WorkloadTracker {
	return &WorkloadTracker{
		agentWorkloads: make(map[string]*CurrentWorkload),
		globalMetrics:  &GlobalWorkloadMetrics{},
	}
}

// GetAgentWorkload returns current workload for an agent
func (wt *WorkloadTracker) GetAgentWorkload(agentID string) *CurrentWorkload {
	return wt.agentWorkloads[agentID]
}

// UpdateAgentWorkload updates workload information for an agent
func (wt *WorkloadTracker) UpdateAgentWorkload(agentID string, activeTasks, queuedTasks []string, capacityLimit float64) {
	estimatedLoad := float64(len(activeTasks))*0.8 + float64(len(queuedTasks))*0.2

	wt.agentWorkloads[agentID] = &CurrentWorkload{
		AgentID:       agentID,
		ActiveTasks:   activeTasks,
		QueuedTasks:   queuedTasks,
		EstimatedLoad: estimatedLoad,
		CapacityLimit: capacityLimit,
		LastUpdated:   time.Now(),
	}

	wt.updateGlobalMetrics()
}

// updateGlobalMetrics recalculates global workload metrics
func (wt *WorkloadTracker) updateGlobalMetrics() {
	var totalUtilization float64
	var activeAgents int
	var bottlenecks []string
	var underutilized []string

	for agentID, workload := range wt.agentWorkloads {
		utilization := workload.EstimatedLoad / workload.CapacityLimit
		totalUtilization += utilization
		activeAgents++

		if utilization > 0.9 {
			bottlenecks = append(bottlenecks, agentID)
		} else if utilization < 0.3 {
			underutilized = append(underutilized, agentID)
		}
	}

	avgUtilization := float64(0)
	if activeAgents > 0 {
		avgUtilization = totalUtilization / float64(activeAgents)
	}

	// Calculate load balance score (lower variance = better balance)
	var variance float64
	for _, workload := range wt.agentWorkloads {
		utilization := workload.EstimatedLoad / workload.CapacityLimit
		variance += math.Pow(utilization-avgUtilization, 2)
	}
	if activeAgents > 0 {
		variance /= float64(activeAgents)
	}
	loadBalanceScore := math.Max(0, 1.0-variance)

	wt.globalMetrics = &GlobalWorkloadMetrics{
		TotalActiveAgents:   activeAgents,
		AverageUtilization:  avgUtilization,
		LoadBalanceScore:    loadBalanceScore,
		BottleneckAgents:    bottlenecks,
		UnderutilizedAgents: underutilized,
	}
}

// NewAgentSelectionModel creates a new ML model for agent selection
func NewAgentSelectionModel() *AgentSelectionModel {
	return &AgentSelectionModel{
		weights: map[string]float64{
			"capability_match": 0.30,
			"performance":      0.25,
			"workload":         0.20,
			"cost":             0.15,
			"collaboration":    0.10,
		},
		learningRate:     0.01,
		decayFactor:      0.95,
		confidenceThresh: 0.7,
	}
}

// recordSelection records a selection for learning
func (asm *AgentSelectionModel) recordSelection(request AgentSelectionRequest, candidate AgentCandidate) {
	// Simple learning mechanism - adjust weights based on success
	// In a real implementation, this would use actual ML algorithms
	log.Printf("Recording selection of agent %s for task type %s",
		candidate.Agent.Name, request.TaskType)
}

// UpdatePerformanceHistory updates performance history for an agent
func (as *AgentSelector) UpdatePerformanceHistory(agentID string, dataPoint PerformanceDataPoint) {
	history, exists := as.performanceCache[agentID]
	if !exists {
		history = &AgentPerformanceHistory{
			AgentID:           agentID,
			PerformancePoints: make([]PerformanceDataPoint, 0),
			CapabilityScores:  make(map[string]float64),
			WorkloadHistory:   make([]WorkloadDataPoint, 0),
			CollaborationData: make([]CollaborationDataPoint, 0),
		}
		as.performanceCache[agentID] = history
	}

	history.PerformancePoints = append(history.PerformancePoints, dataPoint)
	history.LastUpdated = time.Now()

	// Keep only recent data (last 1000 points)
	if len(history.PerformancePoints) > 1000 {
		history.PerformancePoints = history.PerformancePoints[len(history.PerformancePoints)-1000:]
	}

	// Update capability scores
	if dataPoint.TaskType != "" {
		currentScore, exists := history.CapabilityScores[dataPoint.TaskType]
		if !exists {
			currentScore = 0.5 // Default score
		}

		// Exponential moving average
		alpha := 0.1
		newScore := alpha*dataPoint.QualityScore + (1-alpha)*currentScore
		history.CapabilityScores[dataPoint.TaskType] = newScore
	}
}

// GetPerformanceInsights provides insights about agent performance
func (as *AgentSelector) GetPerformanceInsights(agentID string) map[string]interface{} {
	history, exists := as.performanceCache[agentID]
	if !exists {
		return map[string]interface{}{
			"status": "no_data",
		}
	}

	insights := map[string]interface{}{
		"total_tasks":       len(history.PerformancePoints),
		"capability_scores": history.CapabilityScores,
		"last_updated":      history.LastUpdated,
	}

	if len(history.PerformancePoints) > 0 {
		// Calculate trends
		recent := history.PerformancePoints[max(0, len(history.PerformancePoints)-10):]
		var avgSuccess, avgQuality, avgCost float64

		for _, point := range recent {
			avgSuccess += point.SuccessRate
			avgQuality += point.QualityScore
			avgCost += point.CostEfficiency
		}

		count := float64(len(recent))
		insights["recent_success_rate"] = avgSuccess / count
		insights["recent_quality_score"] = avgQuality / count
		insights["recent_cost_efficiency"] = avgCost / count
	}

	return insights
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
