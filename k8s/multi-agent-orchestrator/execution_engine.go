package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/google/uuid"
)

// MultiAgentExecutionEngine orchestrates the execution of multi-agent workflows
type MultiAgentExecutionEngine struct {
	agentRegistry    *AgentRegistry
	communicationHub *CommunicationHub
	storage          WorkflowStorage
	activeExecutions map[string]*WorkflowExecution
	executionQueue   chan string
	mu               sync.RWMutex
	stopChan         chan struct{}
	wg               sync.WaitGroup
}

// ExecutionOptions provides options for workflow execution
type ExecutionOptions struct {
	MaxConcurrentTasks int                    `json:"max_concurrent_tasks"`
	Timeout            time.Duration          `json:"timeout"`
	RetryPolicy        RetryPolicy            `json:"retry_policy"`
	Preferences        map[string]interface{} `json:"preferences"`
}

// RetryPolicy defines how failed tasks should be retried
type RetryPolicy struct {
	MaxRetries    int           `json:"max_retries"`
	RetryDelay    time.Duration `json:"retry_delay"`
	BackoffFactor float64       `json:"backoff_factor"`
}

// NewMultiAgentExecutionEngine creates a new execution engine
func NewMultiAgentExecutionEngine(agentRegistry *AgentRegistry, communicationHub *CommunicationHub, storage WorkflowStorage) *MultiAgentExecutionEngine {
	engine := &MultiAgentExecutionEngine{
		agentRegistry:    agentRegistry,
		communicationHub: communicationHub,
		storage:          storage,
		activeExecutions: make(map[string]*WorkflowExecution),
		executionQueue:   make(chan string, 100),
		stopChan:         make(chan struct{}),
	}

	// Start execution workers
	for i := 0; i < 5; i++ {
		go engine.executionWorker()
	}

	return engine
}

// ExecuteWorkflow starts the execution of a multi-agent workflow
func (mae *MultiAgentExecutionEngine) ExecuteWorkflow(workflowID string) (*WorkflowExecution, error) {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	// Check if workflow is already executing
	if _, exists := mae.activeExecutions[workflowID]; exists {
		return nil, fmt.Errorf("workflow is already executing: %s", workflowID)
	}

	// Load workflow from storage
	workflow, err := mae.storage.LoadWorkflow(workflowID)
	if err != nil {
		return nil, fmt.Errorf("failed to load workflow: %w", err)
	}

	// Validate workflow before execution
	if err := mae.validateWorkflowForExecution(workflow); err != nil {
		return nil, fmt.Errorf("workflow validation failed: %w", err)
	}

	// Create execution context
	execution := &WorkflowExecution{
		ID:             uuid.New().String(),
		WorkflowID:     workflowID,
		Status:         WorkflowStatusExecuting,
		CurrentTasks:   make([]string, 0),
		CompletedTasks: make([]string, 0),
		FailedTasks:    make([]string, 0),
		AgentStates:    make(map[string]AgentState),
		Messages:       make([]AgentMessage, 0),
		Variables:      make(map[string]interface{}),
		Results:        make(map[string]interface{}),
		Metrics: WorkflowMetrics{
			TotalTasks:   len(workflow.Tasks),
			ActiveAgents: len(workflow.Agents),
			LastUpdated:  time.Now(),
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Initialize agent states
	for _, agentID := range workflow.Agents {
		execution.AgentStates[agentID] = AgentState{
			AgentID:      agentID,
			Status:       AgentStatusIdle,
			LastActivity: time.Now(),
			Context:      make(map[string]interface{}),
		}
	}

	// Store execution
	mae.activeExecutions[workflowID] = execution

	// Create communication context
	if err := mae.communicationHub.CreateWorkflowContext(workflowID, workflow.Agents, workflow.Communication); err != nil {
		delete(mae.activeExecutions, workflowID)
		return nil, fmt.Errorf("failed to create communication context: %w", err)
	}

	// Update workflow status
	workflow.Status = WorkflowStatusExecuting
	now := time.Now()
	workflow.StartedAt = &now
	if err := mae.storage.SaveWorkflow(*workflow); err != nil {
		log.Printf("Warning: Failed to update workflow status: %v", err)
	}

	// Queue for execution
	select {
	case mae.executionQueue <- workflowID:
		log.Printf("Queued workflow for execution: %s", workflowID)
	default:
		delete(mae.activeExecutions, workflowID)
		return nil, fmt.Errorf("execution queue is full")
	}

	return execution, nil
}

// PauseWorkflow pauses the execution of a workflow
func (mae *MultiAgentExecutionEngine) PauseWorkflow(workflowID string) error {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	execution, exists := mae.activeExecutions[workflowID]
	if !exists {
		return fmt.Errorf("workflow execution not found: %s", workflowID)
	}

	if execution.Status != WorkflowStatusExecuting {
		return fmt.Errorf("workflow is not executing: %s", execution.Status)
	}

	execution.Status = WorkflowStatusPaused
	execution.UpdatedAt = time.Now()

	log.Printf("Paused workflow execution: %s", workflowID)
	return nil
}

// ResumeWorkflow resumes the execution of a paused workflow
func (mae *MultiAgentExecutionEngine) ResumeWorkflow(workflowID string) error {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	execution, exists := mae.activeExecutions[workflowID]
	if !exists {
		return fmt.Errorf("workflow execution not found: %s", workflowID)
	}

	if execution.Status != WorkflowStatusPaused {
		return fmt.Errorf("workflow is not paused: %s", execution.Status)
	}

	execution.Status = WorkflowStatusExecuting
	execution.UpdatedAt = time.Now()

	// Re-queue for execution
	select {
	case mae.executionQueue <- workflowID:
		log.Printf("Resumed workflow execution: %s", workflowID)
	default:
		return fmt.Errorf("execution queue is full")
	}

	return nil
}

// CancelWorkflow cancels the execution of a workflow
func (mae *MultiAgentExecutionEngine) CancelWorkflow(workflowID string) error {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	execution, exists := mae.activeExecutions[workflowID]
	if !exists {
		return fmt.Errorf("workflow execution not found: %s", workflowID)
	}

	execution.Status = WorkflowStatusCancelled
	execution.UpdatedAt = time.Now()

	// Cleanup
	mae.cleanupExecution(workflowID)

	log.Printf("Cancelled workflow execution: %s", workflowID)
	return nil
}

// GetWorkflowExecution retrieves the execution state of a workflow
func (mae *MultiAgentExecutionEngine) GetWorkflowExecution(workflowID string) (*WorkflowExecution, error) {
	mae.mu.RLock()
	defer mae.mu.RUnlock()

	execution, exists := mae.activeExecutions[workflowID]
	if !exists {
		return nil, fmt.Errorf("workflow execution not found: %s", workflowID)
	}

	// Return a copy
	executionCopy := *execution
	return &executionCopy, nil
}

// Stop gracefully stops the execution engine
func (mae *MultiAgentExecutionEngine) Stop() {
	close(mae.stopChan)
	mae.wg.Wait()
	log.Println("Multi-Agent Execution Engine stopped")
}

// executionWorker processes workflows from the execution queue
func (mae *MultiAgentExecutionEngine) executionWorker() {
	mae.wg.Add(1)
	defer mae.wg.Done()

	for {
		select {
		case workflowID := <-mae.executionQueue:
			mae.executeWorkflowTasks(workflowID)
		case <-mae.stopChan:
			return
		}
	}
}

// executeWorkflowTasks executes the tasks of a workflow
func (mae *MultiAgentExecutionEngine) executeWorkflowTasks(workflowID string) {
	mae.mu.RLock()
	execution, exists := mae.activeExecutions[workflowID]
	mae.mu.RUnlock()

	if !exists {
		log.Printf("Workflow execution not found: %s", workflowID)
		return
	}

	if execution.Status != WorkflowStatusExecuting {
		log.Printf("Workflow is not in executing status: %s (%s)", workflowID, execution.Status)
		return
	}

	// Load workflow
	workflow, err := mae.storage.LoadWorkflow(workflowID)
	if err != nil {
		log.Printf("Failed to load workflow %s: %v", workflowID, err)
		mae.markWorkflowFailed(workflowID, err)
		return
	}

	// Execute tasks based on dependencies
	if err := mae.executeTasks(workflow, execution); err != nil {
		log.Printf("Workflow execution failed %s: %v", workflowID, err)
		mae.markWorkflowFailed(workflowID, err)
		return
	}

	// Check if workflow is complete
	if mae.isWorkflowComplete(execution) {
		mae.markWorkflowCompleted(workflowID)
	}
}

// executeTasks executes workflow tasks respecting dependencies
func (mae *MultiAgentExecutionEngine) executeTasks(workflow *MultiAgentWorkflow, execution *WorkflowExecution) error {
	// Build dependency graph
	dependencyGraph := mae.buildDependencyGraph(workflow.Tasks, workflow.Dependencies)

	// Find tasks that can be executed (no pending dependencies)
	readyTasks := mae.findReadyTasks(workflow.Tasks, dependencyGraph, execution)

	if len(readyTasks) == 0 {
		// No tasks ready, check if we're waiting for something or if we're done
		if len(execution.CompletedTasks) == len(workflow.Tasks) {
			return nil // All tasks completed
		}
		if len(execution.CurrentTasks) == 0 {
			return fmt.Errorf("no tasks ready to execute and none in progress")
		}
		return nil // Waiting for current tasks to complete
	}

	// Execute ready tasks
	for _, task := range readyTasks {
		if err := mae.executeTask(workflow, execution, task); err != nil {
			log.Printf("Failed to execute task %s: %v", task.ID, err)
			execution.FailedTasks = append(execution.FailedTasks, task.ID)
		}
	}

	return nil
}

// executeTask executes a single task
func (mae *MultiAgentExecutionEngine) executeTask(workflow *MultiAgentWorkflow, execution *WorkflowExecution, task WorkflowTask) error {
	// Get assigned agent
	agent, err := mae.agentRegistry.GetAgent(task.AssignedAgentID)
	if err != nil {
		return fmt.Errorf("assigned agent not found: %w", err)
	}

	// Check agent availability
	if agent.Status != AgentStatusIdle {
		return fmt.Errorf("assigned agent is not available: %s", agent.Status)
	}

	// Update agent status
	if err := mae.agentRegistry.UpdateAgentStatus(agent.ID, AgentStatusBusy); err != nil {
		return fmt.Errorf("failed to update agent status: %w", err)
	}

	// Update execution state
	mae.mu.Lock()
	execution.CurrentTasks = append(execution.CurrentTasks, task.ID)
	execution.AgentStates[agent.ID] = AgentState{
		AgentID:      agent.ID,
		Status:       AgentStatusBusy,
		CurrentTask:  task.ID,
		LastActivity: time.Now(),
		Context:      make(map[string]interface{}),
	}
	execution.UpdatedAt = time.Now()
	mae.mu.Unlock()

	// Send task to agent (this would be implemented based on agent communication protocol)
	go mae.sendTaskToAgent(workflow, execution, task, agent)

	log.Printf("Executing task %s on agent %s", task.Name, agent.Name)
	return nil
}

// sendTaskToAgent sends a task to an agent for execution
func (mae *MultiAgentExecutionEngine) sendTaskToAgent(workflow *MultiAgentWorkflow, execution *WorkflowExecution, task WorkflowTask, agent *Agent) {
	// This is a simplified implementation
	// In a real system, this would involve HTTP calls or message queues

	startTime := time.Now()

	// Update execution metrics to track task start
	mae.mu.Lock()
	execution.Metrics.LastUpdated = startTime
	execution.UpdatedAt = startTime

	// Add task context to execution variables
	if execution.Variables == nil {
		execution.Variables = make(map[string]interface{})
	}
	execution.Variables[fmt.Sprintf("task_%s_start_time", task.ID)] = startTime
	execution.Variables[fmt.Sprintf("task_%s_agent", task.ID)] = agent.ID
	execution.Variables[fmt.Sprintf("task_%s_estimated_cost", task.ID)] = task.EstimatedCost

	// Update agent state with task context
	if agentState, exists := execution.AgentStates[agent.ID]; exists {
		agentState.Context["current_task_start"] = startTime
		agentState.Context["current_task_type"] = task.Type
		agentState.Context["current_task_priority"] = task.Priority
		execution.AgentStates[agent.ID] = agentState
	}
	mae.mu.Unlock()

	// Log task execution start with execution context
	log.Printf("Starting task %s (ID: %s) on agent %s for execution %s",
		task.Name, task.ID, agent.Name, execution.ID)

	// Simulate task execution with potential variability based on execution state
	executionTime := time.Duration(task.EstimatedTime)

	// Adjust execution time based on current execution load
	mae.mu.RLock()
	currentLoad := len(execution.CurrentTasks)
	mae.mu.RUnlock()

	if currentLoad > 3 { // High concurrency might slow things down
		executionTime = time.Duration(float64(executionTime) * 1.2)
	}

	// Simulate execution delay
	time.Sleep(executionTime)

	// Calculate actual execution metrics
	actualDuration := time.Since(startTime)
	actualCost := task.EstimatedCost * (float64(actualDuration) / float64(task.EstimatedTime))

	// Update execution with completion metrics
	mae.mu.Lock()
	execution.Variables[fmt.Sprintf("task_%s_actual_duration", task.ID)] = actualDuration
	execution.Variables[fmt.Sprintf("task_%s_actual_cost", task.ID)] = actualCost
	execution.Metrics.TotalCost += actualCost
	execution.Metrics.ExecutionTime += actualDuration
	mae.mu.Unlock()

	// Simulate task completion with execution context
	result := map[string]interface{}{
		"status":          "completed",
		"result":          fmt.Sprintf("Task %s completed successfully", task.Name),
		"execution_id":    execution.ID,
		"actual_duration": actualDuration.String(),
		"actual_cost":     actualCost,
		"agent_id":        agent.ID,
		"completion_time": time.Now(),
		"task_type":       task.Type,
		"task_priority":   task.Priority,
	}

	// Add execution-specific context to result
	if len(execution.CurrentTasks) == 1 { // Last task in current batch
		result["batch_completion"] = true
	}

	if execution.Metrics.TotalCost > workflow.EstimatedCost*1.1 { // Cost overrun
		result["cost_warning"] = "Execution cost exceeding estimates"
	}

	mae.handleTaskCompletion(workflow.ID, task.ID, agent.ID, result, nil)
}

// handleTaskCompletion handles the completion of a task
func (mae *MultiAgentExecutionEngine) handleTaskCompletion(workflowID, taskID, agentID string, result map[string]interface{}, err error) {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	execution, exists := mae.activeExecutions[workflowID]
	if !exists {
		log.Printf("Workflow execution not found for task completion: %s", workflowID)
		return
	}

	// Remove from current tasks
	for i, currentTaskID := range execution.CurrentTasks {
		if currentTaskID == taskID {
			execution.CurrentTasks = append(execution.CurrentTasks[:i], execution.CurrentTasks[i+1:]...)
			break
		}
	}

	// Update based on result
	if err != nil {
		execution.FailedTasks = append(execution.FailedTasks, taskID)
		log.Printf("Task %s failed: %v", taskID, err)
	} else {
		execution.CompletedTasks = append(execution.CompletedTasks, taskID)
		execution.Results[taskID] = result
		log.Printf("Task %s completed successfully", taskID)
	}

	// Update agent state
	execution.AgentStates[agentID] = AgentState{
		AgentID:      agentID,
		Status:       AgentStatusIdle,
		CurrentTask:  "",
		LastActivity: time.Now(),
		Context:      make(map[string]interface{}),
	}

	// Update metrics
	execution.Metrics.CompletedTasks = len(execution.CompletedTasks)
	execution.Metrics.FailedTasks = len(execution.FailedTasks)
	execution.Metrics.LastUpdated = time.Now()
	execution.UpdatedAt = time.Now()

	// Update agent status in registry
	mae.agentRegistry.UpdateAgentStatus(agentID, AgentStatusIdle)

	// Continue execution if there are more tasks
	if execution.Status == WorkflowStatusExecuting {
		select {
		case mae.executionQueue <- workflowID:
		default:
			log.Printf("Failed to re-queue workflow for continued execution: %s", workflowID)
		}
	}
}

// Helper methods

func (mae *MultiAgentExecutionEngine) validateWorkflowForExecution(workflow *MultiAgentWorkflow) error {
	if len(workflow.Tasks) == 0 {
		return fmt.Errorf("workflow has no tasks")
	}

	// Check that all tasks have assigned agents
	for _, task := range workflow.Tasks {
		if task.AssignedAgentID == "" {
			return fmt.Errorf("task %s has no assigned agent", task.Name)
		}

		// Verify agent exists
		if _, err := mae.agentRegistry.GetAgent(task.AssignedAgentID); err != nil {
			return fmt.Errorf("assigned agent %s not found for task %s", task.AssignedAgentID, task.Name)
		}
	}

	return nil
}

func (mae *MultiAgentExecutionEngine) buildDependencyGraph(tasks []WorkflowTask, dependencies []WorkflowDependency) map[string][]string {
	graph := make(map[string][]string)

	for _, dep := range dependencies {
		graph[dep.ToTaskID] = append(graph[dep.ToTaskID], dep.FromTaskID)
	}

	return graph
}

func (mae *MultiAgentExecutionEngine) findReadyTasks(tasks []WorkflowTask, dependencyGraph map[string][]string, execution *WorkflowExecution) []WorkflowTask {
	var readyTasks []WorkflowTask

	for _, task := range tasks {
		// Skip if already completed or failed
		if mae.contains(execution.CompletedTasks, task.ID) || mae.contains(execution.FailedTasks, task.ID) {
			continue
		}

		// Skip if currently executing
		if mae.contains(execution.CurrentTasks, task.ID) {
			continue
		}

		// Check if all dependencies are satisfied
		dependencies := dependencyGraph[task.ID]
		allDependenciesMet := true

		for _, depTaskID := range dependencies {
			if !mae.contains(execution.CompletedTasks, depTaskID) {
				allDependenciesMet = false
				break
			}
		}

		if allDependenciesMet {
			readyTasks = append(readyTasks, task)
		}
	}

	return readyTasks
}

func (mae *MultiAgentExecutionEngine) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func (mae *MultiAgentExecutionEngine) isWorkflowComplete(execution *WorkflowExecution) bool {
	totalTasks := execution.Metrics.TotalTasks
	completedTasks := len(execution.CompletedTasks)
	failedTasks := len(execution.FailedTasks)

	return completedTasks+failedTasks == totalTasks
}

func (mae *MultiAgentExecutionEngine) markWorkflowCompleted(workflowID string) {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	execution := mae.activeExecutions[workflowID]
	execution.Status = WorkflowStatusCompleted
	execution.UpdatedAt = time.Now()

	// Update workflow in storage
	if workflow, err := mae.storage.LoadWorkflow(workflowID); err == nil {
		workflow.Status = WorkflowStatusCompleted
		now := time.Now()
		workflow.CompletedAt = &now
		mae.storage.SaveWorkflow(*workflow)
	}

	mae.cleanupExecution(workflowID)
	log.Printf("Workflow completed: %s", workflowID)
}

func (mae *MultiAgentExecutionEngine) markWorkflowFailed(workflowID string, err error) {
	mae.mu.Lock()
	defer mae.mu.Unlock()

	execution := mae.activeExecutions[workflowID]
	execution.Status = WorkflowStatusFailed
	execution.UpdatedAt = time.Now()

	// Update workflow in storage
	if workflow, loadErr := mae.storage.LoadWorkflow(workflowID); loadErr == nil {
		workflow.Status = WorkflowStatusFailed
		mae.storage.SaveWorkflow(*workflow)
	}

	mae.cleanupExecution(workflowID)
	log.Printf("Workflow failed: %s - %v", workflowID, err)
}

func (mae *MultiAgentExecutionEngine) cleanupExecution(workflowID string) {
	// Cleanup communication context
	mae.communicationHub.CleanupWorkflowContext(workflowID)

	// Remove from active executions after a delay to allow status queries
	go func() {
		time.Sleep(5 * time.Minute)
		mae.mu.Lock()
		delete(mae.activeExecutions, workflowID)
		mae.mu.Unlock()
	}()
}
