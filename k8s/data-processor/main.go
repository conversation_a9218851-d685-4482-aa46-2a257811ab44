package main

// Run the following command to update dependencies:
// go mod tidy

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math/rand"
	"net/http"
	"os"
	"os/signal" // For graceful shutdown
	"strconv"
	"sync"
	"syscall" // For graceful shutdown
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2" // ClickHouse driver
	"github.com/go-redis/redis/v8"             // Redis client
	"github.com/google/uuid"                   // For generating UUIDs for evaluation results and fixing bad ones
	"github.com/segmentio/kafka-go"            // Kafka client
)

// --- Constants ---
const (
	kafkaBroker                     = "kafka:9092"
	kafkaTopic                      = "inference-logs"
	kafkaAgentTopic                 = "agent-performance"
	kafkaWorkflowTopic              = "workflow-execution"
	kafkaPlanningTopic              = "planning-execution"
	kafkaGroupID                    = "data-processor-group"
	clickhouseHost                  = "clickhouse"
	clickhousePort                  = 9000
	clickhouseDB                    = "default"
	redisAddr                       = "redis:6379"
	prometheusURL                   = "http://prometheus:9090" // Prometheus API endpoint (not directly used here, but kept if part of overall project)
	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"         // Redis key prefix for model profiles
	MODEL_PROFILE_UPDATES_CHANNEL   = "model_profile_updates"  // Redis channel for model profile updates
	REDIS_PROMPT_KEY_PREFIX         = "prompt:"
	PROMPT_UPDATES_CHANNEL          = "prompt_updates" // Redis channel for prompt updates

	// ClickHouse table names
	CLICKHOUSE_INFERENCE_LOGS_TABLE     = "inference_logs"
	CLICKHOUSE_EVALUATION_RESULTS_TABLE = "llm_evaluation_results"
	CLICKHOUSE_CURATED_DATA_TABLE       = "curated_data"
	CLICKHOUSE_AGENT_PERFORMANCE_TABLE  = "agent_performance"
	CLICKHOUSE_WORKFLOW_EXECUTION_TABLE = "workflow_execution"
	CLICKHOUSE_PLANNING_LOGS_TABLE      = "planning_logs"

	// Synthetic Data Generation Configuration
	SYNTHETIC_DATA_GENERATION_MODEL_ID = "gemini-2.5-flash-preview-05-20" // Model ID for synthetic data generation
	FEEDBACK_LOOP_INTERVAL             = 3600 * time.Second               // Increased frequency for easier testing
	// FEEDBACK_LOOP_INTERVAL             = 3600 * time.Second // Original interval for production
)

// --- Global Variables (Protected by Mutexes) ---
var (
	clickhouseConn *sql.DB
	redisClient    *redis.Client
	kafkaReader    *kafka.Reader

	// Multi-Agent Kafka readers
	kafkaAgentReader    *kafka.Reader
	kafkaWorkflowReader *kafka.Reader
	kafkaPlanningReader *kafka.Reader

	// Caches for Redis-backed data, protected by mutexes
	modelProfiles  map[string]ModelProfile
	modelProfileMu sync.RWMutex
	prompts        map[string]Prompt
	promptsMu      sync.RWMutex

	rng *rand.Rand // Global random number generator instance

	appCtx    context.Context    // Root context for the application
	appCancel context.CancelFunc // Function to cancel the root context
)

// --- Structs (Matching Kafka messages, Redis, and ClickHouse schemas) ---

// InferenceLog represents the structure of an inference log message received from Kafka
// and stored in ClickHouse.
type InferenceLog struct {
	RequestID           string          `json:"request_id"`
	Timestamp           time.Time       `json:"timestamp"`
	Method              string          `json:"method"`
	Path                string          `json:"path"`
	ClientIP            string          `json:"client_ip"`
	UserAgent           string          `json:"user_agent"`
	SelectedBackendID   string          `json:"selected_backend_id"`
	BackendURL          string          `json:"backend_url"`
	BackendType         string          `json:"backend_type,omitempty"`
	ResponseTimestamp   time.Time       `json:"response_timestamp"`
	LatencyMs           float64         `json:"latency_ms"`
	StatusCode          int32           `json:"status_code"`
	Error               string          `json:"error,omitempty"`
	PolicyIDApplied     string          `json:"policy_id_applied,omitempty"`
	ModelRequested      string          `json:"model_requested,omitempty"`
	ModelUsed           string          `json:"model_used,omitempty"`
	Stream              bool            `json:"stream,omitempty"`
	CPUUsage            float64         `json:"cpu_usage_rate"`
	MemoryUsage         float64         `json:"memory_usage_bytes"`
	TotalCost           float64         `json:"total_cost"`
	InputTokens         int64           `json:"input_tokens,omitempty"`
	OutputTokens        int64           `json:"output_tokens,omitempty"`
	TaskType            string          `json:"task_type,omitempty"`
	ConversationID      string          `json:"conversation_id,omitempty"`
	UserID              string          `json:"user_id,omitempty"`
	UserRoles           []string        `json:"user_roles,omitempty"` // This is the slice
	RequestHeaders      json.RawMessage `json:"request_headers,omitempty"`
	RequestBodySnippet  string          `json:"request_body_snippet,omitempty"`
	ResponseBodySnippet string          `json:"response_body_snippet,omitempty"`
	ResponseHeaders     json.RawMessage `json:"response_headers,omitempty"`
}

type ModelProfile struct {
	ID                  string          `json:"id"`
	Name                string          `json:"name"`
	Aliases             []string        `json:"aliases"`
	Capabilities        []string        `json:"capabilities"`
	PricingTier         string          `json:"pricing_tier"`
	DataSensitivity     string          `json:"data_sensitivity"`
	ExpectedLatencyMs   float64         `json:"expected_latency_ms"`
	ExpectedCost        float64         `json:"expected_cost"`
	BackendURL          string          `json:"url"`
	BackendType         string          `json:"backend_type"`
	CostPerInputToken   float64         `json:"cost_per_input_token"`
	CostPerOutputToken  float64         `json:"cost_per_output_token"`
	CPUCostPerHour      float64         `json:"cpu_cost_per_hour"`
	MemoryCostPerHour   float64         `json:"memory_cost_per_hour"`
	APIKey              string          `json:"api_key,omitempty"`
	CreatedAt           time.Time       `json:"created_at"`
	UpdatedAt           time.Time       `json:"updated_at"`
	Version             string          `json:"version,omitempty"`
	Owner               string          `json:"owner,omitempty"`
	Status              string          `json:"status,omitempty"` // active, inactive, deprecated, experimental
	DocumentationURL    string          `json:"documentation_url,omitempty"`
	License             string          `json:"license,omitempty"`
	FineTuningDetails   string          `json:"fine_tuning_details,omitempty"`
	InputContextLength  int             `json:"input_context_length,omitempty"`
	OutputContextLength int             `json:"output_context_length,omitempty"`
	TrainingDataInfo    string          `json:"training_data_info,omitempty"`
	LastEvaluatedAt     *time.Time      `json:"last_evaluated_at,omitempty"`
	EvaluationMetrics   json.RawMessage `json:"evaluation_metrics,omitempty"`
	ComplianceTags      []string        `json:"compliance_tags,omitempty"`
	Region              string          `json:"region,omitempty"`
	Provider            string          `json:"provider,omitempty"`
	Description         string          `json:"description,omitempty"`
}

type Prompt struct {
	ID          string          `json:"id"`
	Name        string          `json:"name"`
	Version     string          `json:"version"`
	Content     string          `json:"content"`
	Description string          `json:"description,omitempty"`
	Tags        []string        `json:"tags,omitempty"`
	Owner       string          `json:"owner,omitempty"`
	Status      string          `json:"status,omitempty"` // e.g., "draft", "active", "deprecated"
	Metadata    json.RawMessage `json:"metadata,omitempty"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`   // JSON string of conditions
	Action       string          `json:"action"`     // e.g., "ROUTE", "OPTIMIZE", "BLOCK"
	BackendID    string          `json:"backend_id"` // Specific backend ID if action is ROUTE
	Priority     int             `json:"priority"`   // Order of evaluation
	Rules        json.RawMessage `json:"rules"`      // JSON string of task-specific rules or parameters for OPTIMIZE
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata"`      // Additional arbitrary metadata
	RateLimit    int             `json:"rate_limit"`    // For rate limiting policies (e.g., requests per minute)
	Budget       float64         `json:"budget"`        // For cost budgeting policies (e.g., max USD per hour)
	Effect       string          `json:"effect"`        // "ALLOW" or "DENY" for RBAC
	Subjects     []string        `json:"subjects"`      // Users or roles
	ResourceType string          `json:"resource_type"` // e.g., "model", "endpoint"
	ResourceIDs  []string        `json:"resource_ids"`  // Specific resource IDs or "*"
	Permissions  []string        `json:"permissions"`   // Actions allowed/denied
	Status       string          `json:"status"`        // "active", "inactive", etc.
}

// EvaluationRequest defines the structure of an incoming request to the evaluation service.
// It should match the data sent by the data-processor for evaluation.
type EvaluationRequest struct {
	RequestID        string          `json:"request_id"`                  // Original request ID from proxy-gateway
	Prompt           string          `json:"prompt"`                      // The prompt that was sent to the LLM
	LLMResponse      string          `json:"llm_response"`                // The response received from the LLM
	ModelID          string          `json:"model_id"`                    // The ID of the model that generated the response
	TaskType         string          `json:"task_type,omitempty"`         // e.g., "factual_query", "creative_gen", "summarization", "code_generation"
	ExpectedResponse string          `json:"expected_response,omitempty"` // Optional: ground truth or expected answer
	EvaluationType   string          `json:"evaluation_type,omitempty"`   // e.g., "accuracy", "fluency", "safety", "relevance"
	RawMetrics       json.RawMessage `json:"raw_metrics,omitempty"`       // Raw metrics from the evaluation (JSON)
	Metadata         json.RawMessage `json:"metadata,omitempty"`          // Additional metadata (JSON)
}

// LLMEvaluationResult defines the structure for storing LLM evaluation results.
// This is the response format from the evaluation service.
type LLMEvaluationResult struct {
	ID               string          `json:"id"`                          // Unique ID for the evaluation result
	RequestID        string          `json:"request_id"`                  // Original request ID from proxy-gateway
	Prompt           string          `json:"prompt"`                      // The prompt that was sent to the LLM
	LLMResponse      string          `json:"llm_response"`                // The response received from the LLM
	ModelID          string          `json:"model_id"`                    // The ID of the model that generated the response
	TaskType         string          `json:"task_type,omitempty"`         // e.g., "factual_query", "creative_gen"
	EvaluationType   string          `json:"evaluation_type"`             // e.g., "accuracy", "fluency", "safety", "relevance"
	Score            float64         `json:"score"`                       // Numerical score (e.g., 0.0 to 1.0)
	Passed           bool            `json:"passed"`                      // Whether the evaluation passed based on criteria
	Feedback         string          `json:"feedback,omitempty"`          // Detailed feedback or reason for failure
	EvaluatedAt      time.Time       `json:"evaluated_at"`                // Timestamp of the evaluation
	ExpectedResponse string          `json:"expected_response,omitempty"` // Optional: ground truth or expected answer
	RawMetrics       json.RawMessage `json:"raw_metrics,omitempty"`       // Raw metrics from the evaluation (JSON)
	Metadata         json.RawMessage `json:"metadata,omitempty"`          // Additional metadata (JSON)
}

type EvaluationResult struct {
	ID               string    `json:"id"`
	RequestID        string    `json:"request_id"`
	Prompt           string    `json:"prompt"`
	LLMResponse      string    `json:"llm_response"`
	ModelID          string    `json:"model_id"`
	TaskType         string    `json:"task_type,omitempty"`
	EvaluationType   string    `json:"evaluation_type"`
	Score            float64   `json:"score"`
	Passed           bool      `json:"passed"`
	Feedback         string    `json:"feedback,omitempty"`
	EvaluatedAt      time.Time `json:"evaluated_at"`
	ExpectedResponse string    `json:"expected_response,omitempty"`
	RawMetrics       string    `json:"raw_metrics"`
	Metadata         string    `json:"metadata"`
}

type CuratedData struct {
	ID               string    `json:"id"`
	RequestID        string    `json:"request_id"`
	Prompt           string    `json:"prompt"`
	LLMResponse      string    `json:"llm_response"`
	ModelID          string    `json:"model_id"`
	TaskType         string    `json:"task_type,omitempty"`
	GeneratedAt      time.Time `json:"generated_at"`
	EvaluationScore  float64   `json:"evaluation_score"`
	EvaluationPassed bool      `json:"evaluation_passed"`
	EvaluationType   string    `json:"evaluation_type"`
	Feedback         string    `json:"feedback,omitempty"`
	Metadata         string    `json:"metadata"`
	SourceLogID      string    `json:"source_log_id,omitempty"`
	CuratedBy        string    `json:"curated_by,omitempty"`
}

type OptimizationStatus struct {
	OptimalBackendID string    `json:"optimal_backend_id"`
	AverageLatency   float64   `json:"average_latency_ms"`
	Timestamp        time.Time `json:"timestamp"`
	Optimization     string    `json:"optimization"` // "LATENCY", "COST", "BALANCED"
	Message          string    `json:"message"`
}

type OpenAIChatCompletionRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	Stream bool `json:"stream,omitempty"`
}

type OpenAICompletionResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct { // Correct: only one Usage field
		PromptTokens     int64 `json:"prompt_tokens"`
		CompletionTokens int64 `json:"completion_tokens"`
		TotalTokens      int64 `json:"total_tokens"`
	} `json:"usage"`
}

type GeminiGenerateContentRequest struct {
	Contents []struct {
		Role  string `json:"role"`
		Parts []struct {
			Text string `json:"text"`
		} `json:"parts"`
	} `json:"contents"`
	GenerationConfig map[string]interface{} `json:"generationConfig,omitempty"`
}

type GeminiGenerateContentResponse struct {
	Candidates []struct {
		Content struct {
			Parts []struct {
				Text string `json:"text"`
			} `json:"parts"`
			Role string `json:"role"`
		} `json:"content"`
	} `json:"candidates"`
	UsageMetadata struct {
		PromptTokenCount     int64 `json:"promptTokenCount"`
		CandidatesTokenCount int64 `json:"candidatesTokenCount"`
		TotalTokenCount      int64 `json:"totalTokenCount"`
	} `json:"usageMetadata"`
}

type AnthropicMessagesRequest struct {
	Model    string `json:"model"`
	Messages []struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"messages"`
	MaxTokens int `json:"max_tokens"`
}

type AnthropicMessagesResponse struct {
	ID      string `json:"id"`
	Type    string `json:"type"`
	Role    string `json:"role"`
	Content []struct {
		Type string `json:"type"`
		Text string `json:"text"`
	} `json:"content"`
	StopReason string `json:"stop_reason"`
	Model      string `json:"model"`
	Usage      struct {
		InputTokens  int64 `json:"input_tokens"`
		OutputTokens int64 `json:"output_tokens"`
	} `json:"usage"`
}

// Multi-Agent Orchestration Data Structures

type AgentPerformanceLog struct {
	AgentID         string     `json:"agent_id"`
	AgentName       string     `json:"agent_name"`
	AgentType       string     `json:"agent_type"`
	TaskID          string     `json:"task_id"`
	TaskType        string     `json:"task_type"`
	Status          string     `json:"status"` // "started", "completed", "failed"
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	Success         bool       `json:"success"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	InputTokens     int64      `json:"input_tokens"`
	OutputTokens    int64      `json:"output_tokens"`
	Cost            float64    `json:"cost"`
	HealthStatus    string     `json:"health_status"`
	Capabilities    []string   `json:"capabilities"`
	CurrentWorkload int        `json:"current_workload"`
	MaxConcurrency  int        `json:"max_concurrency"`
	LastActiveAt    time.Time  `json:"last_active_at"`
	Metadata        string     `json:"metadata"`
}

type WorkflowExecutionLog struct {
	WorkflowID      string     `json:"workflow_id"`
	WorkflowName    string     `json:"workflow_name"`
	ExecutionID     string     `json:"execution_id"`
	Status          string     `json:"status"` // "started", "running", "completed", "failed", "paused"
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	TasksTotal      int        `json:"tasks_total"`
	TasksCompleted  int        `json:"tasks_completed"`
	TasksFailed     int        `json:"tasks_failed"`
	AgentsInvolved  []string   `json:"agents_involved"`
	TotalCost       float64    `json:"total_cost"`
	CreatedBy       string     `json:"created_by"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	ProgressPercent float64    `json:"progress_percent"`
	Metadata        string     `json:"metadata"`
}

type PlanningExecutionLog struct {
	GoalID          string     `json:"goal_id"`
	GoalDescription string     `json:"goal_description"`
	ExecutionID     string     `json:"execution_id"`
	Status          string     `json:"status"` // "started", "running", "completed", "failed"
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	TasksTotal      int        `json:"tasks_total"`
	TasksCompleted  int        `json:"tasks_completed"`
	TasksFailed     int        `json:"tasks_failed"`
	TotalCost       float64    `json:"total_cost"`
	CreatedBy       string     `json:"created_by"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	ProgressPercent float64    `json:"progress_percent"`
	Metadata        string     `json:"metadata"`
}

// min returns the smaller of two integers.
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// --- Initialization ---

func init() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	source := rand.NewSource(time.Now().UnixNano())
	rng = rand.New(source)

	// ClickHouse Connection
	chHost := os.Getenv("CLICKHOUSE_HOST")
	chPort := os.Getenv("CLICKHOUSE_PORT")
	chDB := os.Getenv("CLICKHOUSE_DB")
	chUser := os.Getenv("CLICKHOUSE_USER")
	chPassword := os.Getenv("CLICKHOUSE_PASSWORD")

	if chHost == "" || chPort == "" || chDB == "" || chUser == "" || chPassword == "" {
		log.Fatalf("Missing one or more ClickHouse environment variables (CLICKHOUSE_HOST, CLICKHOUSE_PORT, CLICKHOUSE_DB, CLICKHOUSE_USER, CLICKHOUSE_PASSWORD)")
	}

	clickhouseDSN := fmt.Sprintf("tcp://%s:%s/%s?username=%s&password=%s",
		chHost, chPort, chDB, chUser, chPassword)

	var err error
	clickhouseConn, err = sql.Open("clickhouse", clickhouseDSN)
	if err != nil {
		log.Fatalf("Failed to open ClickHouse connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err = clickhouseConn.PingContext(ctx); err != nil {
		log.Fatalf("Failed to ping ClickHouse with DSN '%s': %v", clickhouseDSN, err)
	}
	log.Println("Successfully connected to ClickHouse.")

	// Redis Connection
	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0, // Default DB
	})
	ctxRedis, cancelRedis := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancelRedis()
	if _, err = redisClient.Ping(ctxRedis).Result(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	// Initial load of model profiles and prompts from Redis with retries
	maxRetries := 10
	retryDelay := 5 * time.Second
	for i := 0; i < maxRetries; i++ {
		log.Printf("Attempt %d/%d: Loading model profiles and prompts from Redis...", i+1, maxRetries)

		// Load Model Profiles (locking handled by caller, i.e., here in init)
		loadErr := loadModelProfilesFromRedis(context.Background())
		if loadErr != nil {
			log.Printf("Attempt %d/%d: Error loading model profiles from Redis: %v.", i+1, maxRetries, loadErr)
			time.Sleep(retryDelay)
			continue
		}

		// Load Prompts (locking handled by caller, i.e., here in init)
		loadErr = loadPromptsFromRedis(context.Background())
		if loadErr != nil {
			log.Printf("Attempt %d/%d: Error loading prompts from Redis: %v.", i+1, maxRetries, loadErr)
			time.Sleep(retryDelay)
			continue
		}

		if len(modelProfiles) > 0 && len(prompts) > 0 {
			log.Printf("Successfully loaded %d model profiles and %d prompts from Redis after %d attempts.", len(modelProfiles), len(prompts), i+1)
			break // Exit loop if data loaded
		} else {
			log.Printf("Attempt %d/%d: Loaded 0 model profiles (%d) or 0 prompts (%d). Retrying in %v...", i+1, maxRetries, len(modelProfiles), len(prompts), retryDelay)
			time.Sleep(retryDelay)
		}

		if i == maxRetries-1 {
			log.Println("Failed to load model profiles and prompts from Redis after all attempts. Dashboard data may be incomplete.")
			os.Exit(1) // Exit with error code
		}
	}

	// Initialize Kafka Readers
	kafkaReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaTopic,
		GroupID:  kafkaGroupID,
		MaxBytes: 10e6, // 10MB
	})
	log.Println("Kafka inference logs reader initialized.")

	// Initialize Multi-Agent Kafka readers
	kafkaAgentReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaAgentTopic,
		GroupID:  kafkaGroupID + "-agent",
		MaxBytes: 10e6, // 10MB
	})
	log.Println("Kafka agent performance reader initialized.")

	kafkaWorkflowReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaWorkflowTopic,
		GroupID:  kafkaGroupID + "-workflow",
		MaxBytes: 10e6, // 10MB
	})
	log.Println("Kafka workflow execution reader initialized.")

	kafkaPlanningReader = kafka.NewReader(kafka.ReaderConfig{
		Brokers:  []string{kafkaBroker},
		Topic:    kafkaPlanningTopic,
		GroupID:  kafkaGroupID + "-planning",
		MaxBytes: 10e6, // 10MB
	})
	log.Println("Kafka planning execution reader initialized.")

	// Create root context for graceful shutdown
	appCtx, appCancel = context.WithCancel(context.Background())

	// Start Redis Pub/Sub listeners for cache updates
	go setupRedisPubSub()
}

// --- Redis Cache Management ---

// setupRedisPubSub subscribes to Redis channels for model profile and prompt updates
// to keep the in-memory caches synchronized.
func setupRedisPubSub() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL, PROMPT_UPDATES_CHANNEL)
	defer pubsub.Close()

	receiveCtx, receiveCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer receiveCancel()
	var err error
	if _, err = pubsub.Receive(receiveCtx); err != nil && err != context.DeadlineExceeded {
		log.Printf("Failed to subscribe to Redis Pub/Sub or timed out during initial receive: %v", err)
		return
	} else if err == context.DeadlineExceeded {
		log.Println("Timed out waiting for Redis Pub/Sub subscription confirmation, continuing...")
	}

	ch := pubsub.Channel()
	log.Printf("Subscribed to Redis Pub/Sub channels '%s' and '%s'.", MODEL_PROFILE_UPDATES_CHANNEL, PROMPT_UPDATES_CHANNEL)

	for {
		select {
		case msg := <-ch:
			log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
			switch msg.Channel {
			case MODEL_PROFILE_UPDATES_CHANNEL:
				modelProfileMu.Lock()
				if err = loadModelProfilesFromRedis(context.Background()); err != nil {
					log.Printf("Error refreshing model profiles cache: %v", err)
				} else {
					log.Println("Model profiles cache refreshed due to update.")
				}
				modelProfileMu.Unlock()
			case PROMPT_UPDATES_CHANNEL:
				promptsMu.Lock()
				if err = loadPromptsFromRedis(context.Background()); err != nil {
					log.Printf("Error refreshing prompts cache: %v", err)
				} else {
					log.Println("Prompts cache refreshed due to update.")
				}
				promptsMu.Unlock()
			}
		case <-appCtx.Done(): // Listen for application shutdown signal
			log.Println("Redis Pub/Sub listener shutting down.")
			return
		}
	}
}

// loadModelProfilesFromRedis fetches all model profiles from Redis and updates the in-memory cache.
// It assumes the caller handles locking of modelProfileMu.
func loadModelProfilesFromRedis(ctx context.Context) error {
	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil { // Check for redis.Nil specifically, which means no keys found
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}
	if err == redis.Nil { // If no keys found, return without error but with empty map
		modelProfiles = newModelProfiles // Assign empty map
		return nil
	}
	for _, key := range keys {
		val, getErr := redisClient.Get(ctx, key).Result()
		if getErr != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, getErr)
			continue
		}
		var profile ModelProfile
		unmarshalErr := json.Unmarshal([]byte(val), &profile)
		if unmarshalErr != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, unmarshalErr)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfiles = newModelProfiles
	return nil
}

// loadPromptsFromRedis fetches all prompts from Redis and updates the in-memory cache.
// It assumes the caller handles locking of promptsMu.
func loadPromptsFromRedis(ctx context.Context) error {
	newPrompts := make(map[string]Prompt)
	keys, err := redisClient.Keys(ctx, REDIS_PROMPT_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil { // Check for redis.Nil specifically
		return fmt.Errorf("failed to get prompt keys from Redis: %w", err)
	}
	if err == redis.Nil { // If no keys found, return without error but with empty map
		prompts = newPrompts // Assign empty map
		return nil
	}
	for _, key := range keys {
		val, getErr := redisClient.Get(ctx, key).Result()
		if getErr != nil {
			log.Printf("Error getting prompt %s from Redis: %v", key, getErr)
			continue
		}
		var prompt Prompt
		unmarshalErr := json.Unmarshal([]byte(val), &prompt)
		if unmarshalErr != nil {
			log.Printf("Error unmarshalling prompt %s: %v", key, unmarshalErr)
			continue
		}
		newPrompts[fmt.Sprintf("%s:%s", prompt.ID, prompt.Version)] = prompt // Key by ID:Version
	}
	prompts = newPrompts
	return nil
}

// --- Kafka Consumer ---

// startKafkaConsumer reads messages from Kafka and inserts them into ClickHouse.
func startKafkaConsumer(ctx context.Context) {
	log.Printf("Starting Kafka consumer for topic: %s, group: %s", kafkaTopic, kafkaGroupID)
	for {
		select {
		case <-ctx.Done():
			log.Println("Kafka consumer shutting down.")
			return
		default:
			m, err := kafkaReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return // Context canceled, gracefully exit
				}
				log.Printf("Error fetching Kafka message: %v", err)
				time.Sleep(1 * time.Second) // Small delay before retrying
				continue
			}

			var logEntry InferenceLog
			if err := json.Unmarshal(m.Value, &logEntry); err != nil {
				log.Printf("Error unmarshalling Kafka message into InferenceLog: %v, message: %s", err, string(m.Value))
				// Commit the message even if unmarshalling fails to avoid reprocessing bad messages
				if commitErr := kafkaReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing Kafka message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			// Fix for cases where UUIDs might be malformed in ClickHouse due to case sensitivity or hyphens
			// Note: The `uuid.Parse` function expects canonical form (with hyphens)
			// so it's safer to just parse and reformat, letting uuid.Parse handle variations.
			parsed, err := uuid.Parse(logEntry.RequestID)
			if err == nil {
				logEntry.RequestID = parsed.String() // Reformat to standard UUID string with hyphens
			} else {
				log.Printf("Warning: Malformed RequestID '%s' from Kafka. Will use a generated new UUID for ClickHouse. Error: %v", logEntry.RequestID, err)
				logEntry.RequestID = uuid.New().String()
			}

			// Ensure TaskType is not empty, default to "unknown"
			if logEntry.TaskType == "" {
				logEntry.TaskType = "unknown"
			}
			// Ensure PolicyIDApplied is not empty, default to "none"
			if logEntry.PolicyIDApplied == "" {
				logEntry.PolicyIDApplied = "none"
			}
			// Ensure ModelUsed is not empty, default to "unknown"
			if logEntry.ModelUsed == "" {
				logEntry.ModelUsed = "unknown"
			}
			// Ensure UserRoles is not nil or empty. ClickHouse expects an array.
			if logEntry.UserRoles == nil {
				logEntry.UserRoles = []string{}
			}

			if err := insertInferenceLog(ctx, logEntry); err != nil {
				log.Printf("Error inserting inference log into ClickHouse: %v, logEntry: %+v", err, logEntry)
				// Depending on error, you might want to retry or send to a dead-letter queue
			} else {
				log.Printf("Successfully processed and inserted log for request_id: %s", logEntry.RequestID)

				// Send evaluation request
				if err := sendEvaluationRequest(ctx, logEntry); err != nil {
					log.Printf("Error sending evaluation request: %v", err)
				}
			}

			if commitErr := kafkaReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing Kafka message: %v", commitErr)
			}
		}
	}
}

// insertInferenceLog inserts a single InferenceLog into the ClickHouse database.
func insertInferenceLog(ctx context.Context, logEntry InferenceLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback() // Rollback if not committed

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			request_id, timestamp, method, path, client_ip, user_agent, selected_backend_id, backend_url, backend_type,
			response_timestamp, latency_ms, status_code, error, policy_id_applied, model_requested, model_used, stream,
			cpu_usage_rate, memory_usage_bytes, total_cost, input_tokens, output_tokens, task_type, conversation_id, user_id,
			user_roles, request_headers, request_body_snippet, response_body_snippet, response_headers
		) VALUES (
			?, ?, ?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_INFERENCE_LOGS_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for inference logs: %w", err)
	}
	defer stmt.Close()

	// Convert bool to int32 for ClickHouse UInt8
	streamValue := int32(0)
	if logEntry.Stream {
		streamValue = 1
	}

	// Ensure RawMessage fields are handled correctly. If they are empty, send an empty JSON object string.
	requestHeadersStr := string(logEntry.RequestHeaders)
	if requestHeadersStr == "" {
		requestHeadersStr = "{}"
	}
	responseHeadersStr := string(logEntry.ResponseHeaders)
	if responseHeadersStr == "" {
		responseHeadersStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		logEntry.RequestID,
		logEntry.Timestamp,
		logEntry.Method,
		logEntry.Path,
		logEntry.ClientIP,
		logEntry.UserAgent,
		logEntry.SelectedBackendID,
		logEntry.BackendURL,
		logEntry.BackendType,
		logEntry.ResponseTimestamp,
		logEntry.LatencyMs,
		logEntry.StatusCode,
		logEntry.Error,
		logEntry.PolicyIDApplied,
		logEntry.ModelRequested,
		logEntry.ModelUsed,
		streamValue, // Pass as int32
		logEntry.CPUUsage,
		logEntry.MemoryUsage,
		logEntry.TotalCost,
		logEntry.InputTokens,
		logEntry.OutputTokens,
		logEntry.TaskType,
		logEntry.ConversationID,
		logEntry.UserID,
		logEntry.UserRoles, // Directly pass the []string slice, driver handles Array(String)
		requestHeadersStr,
		logEntry.RequestBodySnippet,
		logEntry.ResponseBodySnippet,
		responseHeadersStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for inference log: %w", err)
	}

	return tx.Commit()
}

// sendEvaluationRequest sends an evaluation request to the evaluation service.
func sendEvaluationRequest(ctx context.Context, logEntry InferenceLog) error {
	evaluationServiceURL := os.Getenv("EVALUATION_SERVICE_URL")
	if evaluationServiceURL == "" {
		log.Println("EVALUATION_SERVICE_URL not set, skipping evaluation")
		return nil
	}

	evaluationRequest := EvaluationRequest{
		RequestID:   logEntry.RequestID,
		Prompt:      logEntry.RequestBodySnippet, // Or extract full prompt if available
		LLMResponse: logEntry.ResponseBodySnippet,
		ModelID:     logEntry.ModelUsed,
		TaskType:    logEntry.TaskType,
	}

	jsonValue, err := json.Marshal(evaluationRequest)
	if err != nil {
		return fmt.Errorf("failed to marshal evaluation request: %w", err)
	}

	log.Printf("Sending evaluation request to: %s/evaluate, RequestID: %s", evaluationServiceURL, logEntry.RequestID)
	resp, err := http.Post(evaluationServiceURL+"/evaluate", "application/json", bytes.NewBuffer(jsonValue))
	if err != nil {
		return fmt.Errorf("failed to send evaluation request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read evaluation response body: %w", err)
		}

		var evalResult LLMEvaluationResult
		if err := json.Unmarshal(body, &evalResult); err != nil {
			return fmt.Errorf("failed to unmarshal evaluation response: %w", err)
		}

		// Convert LLMEvaluationResult to EvaluationResult
		evaluationResult := EvaluationResult{
			ID:               evalResult.ID,
			RequestID:        evalResult.RequestID,
			Prompt:           evalResult.Prompt,
			LLMResponse:      evalResult.LLMResponse,
			ModelID:          evalResult.ModelID,
			TaskType:         evalResult.TaskType,
			EvaluationType:   evalResult.EvaluationType,
			Score:            evalResult.Score,
			Passed:           evalResult.Passed,
			Feedback:         evalResult.Feedback,
			EvaluatedAt:      evalResult.EvaluatedAt,
			ExpectedResponse: evalResult.ExpectedResponse,
			RawMetrics:       string(evalResult.RawMetrics),
			Metadata:         string(evalResult.Metadata),
		}

		// Insert evaluation result into ClickHouse
		if err := insertEvaluationResult(ctx, evaluationResult); err != nil {
			return fmt.Errorf("failed to insert evaluation result: %w", err)
		}

		log.Printf("Successfully sent evaluation request for RequestID: %s, Evaluation Score: %.2f", logEntry.RequestID, evalResult.Score)
		return nil
	} else {
		return fmt.Errorf("evaluation service returned non-success status code: %d", resp.StatusCode)
	}
}

// --- Synthetic Data Generation Loop ---

// startSyntheticDataGeneration periodically generates synthetic LLM inference data.
func startSyntheticDataGeneration(ctx context.Context) {
	ticker := time.NewTicker(FEEDBACK_LOOP_INTERVAL)
	defer ticker.Stop()

	log.Printf("Starting synthetic data generation feedback loop, interval: %v", FEEDBACK_LOOP_INTERVAL)

	for {
		select {
		case <-ctx.Done():
			log.Println("Synthetic data generation shutting down.")
			return
		case <-ticker.C:
			log.Println("Running synthetic data generation and evaluation...")
			profile, err := getActiveModelProfile(SYNTHETIC_DATA_GENERATION_MODEL_ID)
			if err != nil {
				log.Printf("Synthetic data generation: %v. Cannot generate synthetic data.", err)
				continue
			}

			// Get a random prompt for synthetic data
			prompt, err := getRandomPrompt()
			if err != nil {
				log.Printf("Synthetic data generation: %v. Cannot generate synthetic data.", err)
				continue
			}
			log.Printf("Selected random prompt for synthetic data: %s...", truncateString(prompt.Content, 50))

			// Simulate LLM call using the selected model profile
			llmResponseContent, statusCode, callErr := callLLM(ctx, profile, prompt.Content)
			if callErr != nil {
				log.Printf("Synthetic data generation: Error calling LLM '%s': %v", profile.Name, callErr)
				// Generate a synthetic log for the error
				// Use the actual status code from the LLM call error
				syntheticLog := createSyntheticLogEntry(profile, prompt.Content, "", 0, 0, int32(statusCode), callErr.Error(), prompt.Tags[0]) // Use first tag as task type
				if insertErr := insertInferenceLog(ctx, syntheticLog); insertErr != nil {
					log.Printf("Error inserting synthetic error log: %v", insertErr)
				}
				continue
			}

			// Simulate token calculation (simplified)
			inputTokens := int64(len(prompt.Content) / 4)      // Approx. 4 chars per token
			outputTokens := int64(len(llmResponseContent) / 4) // Approx. 4 chars per token
			if inputTokens == 0 {
				inputTokens = 10
			} // Ensure non-zero for cost calculation
			if outputTokens == 0 {
				outputTokens = 20
			} // Ensure non-zero for cost calculation

			totalCost := (float64(inputTokens)*profile.CostPerInputToken + float64(outputTokens)*profile.CostPerOutputToken)

			// Create synthetic inference log
			syntheticLog := createSyntheticLogEntry(profile, prompt.Content, llmResponseContent, inputTokens, outputTokens, int32(statusCode), "", prompt.Tags[0]) // Use first tag as task type
			syntheticLog.TotalCost = totalCost

			if err := insertInferenceLog(ctx, syntheticLog); err != nil {
				log.Printf("Error inserting synthetic inference log: %v", err)
				continue
			}

			// Send evaluation request
			if err := sendEvaluationRequest(ctx, syntheticLog); err != nil {
				log.Printf("Error sending evaluation request: %v", err)
			}

			// Simulate evaluation and store
			// For simplicity, generate a score based on success/failure and add some randomness
			evaluationScore := 0.0
			evaluationPassed := false
			if statusCode == http.StatusOK {
				evaluationScore = 0.8 + rng.Float64()*0.2 // Score between 0.8 and 1.0 for success
				evaluationPassed = evaluationScore > 0.85
			} else {
				evaluationScore = rng.Float64() * 0.5 // Lower score for errors
				evaluationPassed = false
			}

			evaluationResult := EvaluationResult{
				ID:             uuid.New().String(),
				RequestID:      syntheticLog.RequestID,
				Prompt:         prompt.Content,
				LLMResponse:    llmResponseContent,
				ModelID:        profile.ID,
				TaskType:       syntheticLog.TaskType, // Use the task type from the log
				EvaluationType: "automated-quality",
				Score:          evaluationScore,
				Passed:         evaluationPassed,
				Feedback:       fmt.Sprintf("Automated evaluation score: %.2f", evaluationScore),
				EvaluatedAt:    time.Now().UTC(),
				RawMetrics:     fmt.Sprintf(`{"factuality": %.2f, "coherence": %.2f}`, rng.Float64(), rng.Float64()),
				Metadata:       `{"synthetic": true}`,
			}
			if err := insertEvaluationResult(ctx, evaluationResult); err != nil {
				log.Printf("Error inserting synthetic evaluation result: %v", err)
			} else {
				log.Printf("Successfully generated and evaluated synthetic data for model %s (Score: %.2f).", profile.Name, evaluationScore)
			}

			// Optionally, if evaluation passed and score is high, curate the data
			if evaluationPassed && evaluationScore >= 0.95 {
				curatedEntry := CuratedData{
					ID:               uuid.New().String(),
					RequestID:        syntheticLog.RequestID,
					Prompt:           prompt.Content,
					LLMResponse:      llmResponseContent,
					ModelID:          profile.ID,
					TaskType:         syntheticLog.TaskType, // Use the task type from the log
					GeneratedAt:      time.Now().UTC(),
					EvaluationScore:  evaluationResult.Score,
					EvaluationPassed: evaluationResult.Passed,
					EvaluationType:   evaluationResult.EvaluationType,
					Feedback:         "Automated curation: high quality synthetic example.",
					Metadata:         `{"synthetic_curated": true}`,
					SourceLogID:      syntheticLog.RequestID,
					CuratedBy:        "data-processor-automated",
				}
				if err := insertCuratedData(ctx, curatedEntry); err != nil {
					log.Printf("Error inserting synthetic curated data: %v", err)
				} else {
					log.Printf("Successfully curated synthetic data for model %s.", profile.Name)
				}
			}
		}
	}
}

// getActiveModelProfile retrieves an active model profile by ID from the cache.
func getActiveModelProfile(modelID string) (ModelProfile, error) {
	modelProfileMu.RLock()
	profile, ok := modelProfiles[modelID]
	modelProfileMu.RUnlock()
	if !ok {
		return ModelProfile{}, fmt.Errorf("model profile '%s' not found", modelID)
	}
	if profile.Status != "active" {
		return ModelProfile{}, fmt.Errorf("model profile '%s' is not active (status: %s)", modelID, profile.Status)
	}
	return profile, nil
}

// getRandomPrompt fetches a random prompt from Redis.
func getRandomPrompt() (Prompt, error) {
	promptsMu.RLock()
	defer promptsMu.RUnlock()

	if len(prompts) == 0 {
		return Prompt{}, fmt.Errorf("no prompts loaded in cache")
	}

	keys := make([]string, 0, len(prompts))
	for k := range prompts {
		keys = append(keys, k)
	}

	if len(keys) == 0 {
		return Prompt{}, fmt.Errorf("no prompts available in keys slice")
	}

	randomIndex := rng.Intn(len(keys))
	randomKey := keys[randomIndex]
	randomPrompt := prompts[randomKey]

	return randomPrompt, nil
}

// callLLM makes the actual HTTP call to the LLM backend for synthetic data generation.
func callLLM(ctx context.Context, profile ModelProfile, promptContent string) (string, int, error) {
	client := &http.Client{Timeout: 30 * time.Second}

	var reqBodyBytes []byte
	var req *http.Request
	var err error

	switch profile.BackendType {
	case "openai-external":
		requestBody := OpenAIChatCompletionRequest{
			Model: profile.ID,
			Messages: []struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			}{
				{Role: "user", Content: promptContent},
			},
			Stream: false,
		}
		reqBodyBytes, err = json.Marshal(requestBody)
		if err != nil {
			return "", 0, fmt.Errorf("failed to marshal OpenAI request body: %w", err)
		}
		req, err = http.NewRequestWithContext(ctx, "POST", profile.BackendURL, bytes.NewBuffer(reqBodyBytes))
		if err != nil {
			return "", 0, fmt.Errorf("failed to create OpenAI request: %w", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+profile.APIKey)
	case "google-external":
		requestBody := GeminiGenerateContentRequest{
			Contents: []struct {
				Role  string `json:"role"`
				Parts []struct {
					Text string `json:"text"`
				} `json:"parts"`
			}{
				{
					Role: "user",
					Parts: []struct {
						Text string `json:"text"`
					}{
						{Text: promptContent},
					},
				},
			},
		}
		reqBodyBytes, err = json.Marshal(requestBody)
		if err != nil {
			return "", 0, fmt.Errorf("failed to marshal Gemini request body: %w", err)
		}
		req, err = http.NewRequestWithContext(ctx, "POST", profile.BackendURL, bytes.NewBuffer(reqBodyBytes))
		if err != nil {
			return "", 0, fmt.Errorf("failed to create Gemini request: %w", err)
		}
		req.Header.Set("Content-Type", "application/json")
		// Use X-Goog-Api-Key header for consistency with proxy-gateway
		if profile.APIKey != "" {
			req.Header.Set("X-Goog-Api-Key", profile.APIKey)
		} else {
			log.Printf("Warning: No Google API Key provided for synthetic data generation model %s.", profile.ID)
		}
	case "anthropic-external":
		requestBody := AnthropicMessagesRequest{
			Model: profile.ID,
			Messages: []struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			}{
				{Role: "user", Content: promptContent},
			},
			MaxTokens: 1024,
		}
		reqBodyBytes, err = json.Marshal(requestBody)
		if err != nil {
			return "", 0, fmt.Errorf("failed to marshal Anthropic request body: %w", err)
		}
		req, err = http.NewRequestWithContext(ctx, "POST", profile.BackendURL, bytes.NewBuffer(reqBodyBytes))
		if err != nil {
			return "", 0, fmt.Errorf("failed to create Anthropic request: %w", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Api-Key", profile.APIKey)
		req.Header.Set("anthropic-version", "2023-06-01")
	default:
		log.Printf("Simulating LLM call for backend type: %s", profile.BackendType)
		return fmt.Sprintf("Synthetic response from %s for prompt: %s", profile.Name, promptContent), http.StatusOK, nil
	}

	if err != nil {
		return "", 0, err
	}

	resp, err := client.Do(req)
	if err != nil {
		return "", 0, fmt.Errorf("LLM request failed for %s: %w", profile.Name, err)
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", resp.StatusCode, fmt.Errorf("failed to read LLM response body for %s: %w", profile.Name, err)
	}

	// For non-2xx status codes, return the error and status code, but don't try to parse content.
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return "", resp.StatusCode, fmt.Errorf("LLM returned non-2xx status code %d: %s", resp.StatusCode, string(responseBody))
	}

	content := ""
	switch profile.BackendType {
	case "openai-external":
		var openaiResp OpenAICompletionResponse
		if err := json.Unmarshal(responseBody, &openaiResp); err == nil {
			if len(openaiResp.Choices) > 0 && openaiResp.Choices[0].Message.Content != "" {
				content = openaiResp.Choices[0].Message.Content
			}
		}
	case "google-external":
		var geminiResp GeminiGenerateContentResponse
		if err := json.Unmarshal(responseBody, &geminiResp); err == nil {
			if len(geminiResp.Candidates) > 0 &&
				geminiResp.Candidates[0].Content.Parts != nil &&
				len(geminiResp.Candidates[0].Content.Parts) > 0 {
				content = geminiResp.Candidates[0].Content.Parts[0].Text
			}
		}
	case "anthropic-external":
		var anthropicResp AnthropicMessagesResponse
		if err := json.Unmarshal(responseBody, &anthropicResp); err == nil {
			if len(anthropicResp.Content) > 0 && anthropicResp.Content[0].Type == "text" {
				content = anthropicResp.Content[0].Text
			}
		}
	default:
		content = string(responseBody)
	}

	return content, resp.StatusCode, nil
}

// createSyntheticLogEntry generates a synthetic InferenceLog entry.
func createSyntheticLogEntry(profile ModelProfile, promptContent, llmResponse string, inputTokens, outputTokens int64, statusCode int32, errMsg, taskType string) InferenceLog {
	now := time.Now().UTC()
	latency := 0.0
	if statusCode == http.StatusOK {
		latency = profile.ExpectedLatencyMs + rng.Float64()*50 // Add some random variance
	} else {
		latency = rng.Float64() * 2000 // Longer latency for errors
	}

	if errMsg == "" && statusCode != http.StatusOK {
		errMsg = "synthetic error"
	}

	totalCost := (float64(inputTokens)*profile.CostPerInputToken + float64(outputTokens)*profile.CostPerOutputToken)

	return InferenceLog{
		RequestID:           uuid.New().String(),
		Timestamp:           now.Add(-time.Duration(rng.Intn(300)) * time.Second), // Random timestamp within last 5 minutes
		Method:              "POST",
		Path:                "/v1/chat/completions",
		ClientIP:            fmt.Sprintf("192.168.1.%d", rng.Intn(255)),
		UserAgent:           "SyntheticDataGenerator/1.0",
		SelectedBackendID:   profile.ID,
		BackendURL:          profile.BackendURL,
		BackendType:         profile.BackendType,
		ResponseTimestamp:   now,
		LatencyMs:           latency,
		StatusCode:          statusCode,
		Error:               errMsg,
		PolicyIDApplied:     "synthetic-policy",
		ModelRequested:      profile.ID,
		ModelUsed:           profile.ID,
		Stream:              false,
		CPUUsage:            rng.Float64() * 10,
		MemoryUsage:         rng.Float64() * 500 * 1024 * 1024,
		TotalCost:           totalCost,
		InputTokens:         inputTokens,
		OutputTokens:        outputTokens,
		TaskType:            taskType,
		ConversationID:      uuid.New().String(),
		UserID:              "synthetic-user-" + strconv.Itoa(rng.Intn(10)),
		UserRoles:           []string{"synthetic", "tester"},
		RequestHeaders:      json.RawMessage(`{"Content-Type": "application/json"}`),
		RequestBodySnippet:  fmt.Sprintf(`{"prompt": "%s..."}`, truncateString(promptContent, 50)),
		ResponseBodySnippet: fmt.Sprintf(`{"response": "%s..."}`, truncateString(llmResponse, 50)),
		ResponseHeaders:     json.RawMessage(`{"Content-Type": "application/json"}`),
	}
}

// insertEvaluationResult inserts an evaluation result into ClickHouse.
func insertEvaluationResult(ctx context.Context, evalResult EvaluationResult) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for evaluation result: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			id, request_id, prompt, llm_response, model_id, task_type,
			evaluation_type, score, passed, feedback, evaluated_at,
			expected_response, raw_metrics, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?
		)
	`, CLICKHOUSE_EVALUATION_RESULTS_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for evaluation results: %w", err)
	}
	defer stmt.Close()

	// Convert bool to int32 for ClickHouse UInt8
	passedValue := int32(0)
	if evalResult.Passed {
		passedValue = 1
	}

	// Ensure RawMetrics and Metadata are valid JSON strings or empty objects
	rawMetricsStr := evalResult.RawMetrics
	if rawMetricsStr == "" {
		rawMetricsStr = "{}"
	}
	metadataStr := evalResult.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		evalResult.ID,
		evalResult.RequestID,
		evalResult.Prompt,
		evalResult.LLMResponse,
		evalResult.ModelID,
		evalResult.TaskType,
		evalResult.EvaluationType,
		evalResult.Score,
		passedValue, // Pass as int32
		evalResult.Feedback,
		evalResult.EvaluatedAt,
		evalResult.ExpectedResponse,
		rawMetricsStr, // Ensure JSON string
		metadataStr,   // Ensure JSON string
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for evaluation result: %w", err)
	}

	return tx.Commit()
}

// insertCuratedData inserts curated data into ClickHouse.
func insertCuratedData(ctx context.Context, curatedData CuratedData) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for curated data: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			id, request_id, prompt, llm_response, model_id, task_type, generated_at,
			evaluation_score, evaluation_passed, evaluation_type, feedback, metadata, source_log_id, curated_by
		) VALUES (
			?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_CURATED_DATA_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for curated data: %w", err)
	}
	defer stmt.Close()

	// Convert bool to int32 for ClickHouse UInt8
	evaluationPassedValue := int32(0)
	if curatedData.EvaluationPassed {
		evaluationPassedValue = 1
	}

	// Ensure Metadata is a valid JSON string or empty object
	metadataStr := curatedData.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		curatedData.ID,
		curatedData.RequestID,
		curatedData.Prompt,
		curatedData.LLMResponse,
		curatedData.ModelID,
		curatedData.TaskType,
		curatedData.GeneratedAt,
		curatedData.EvaluationScore,
		evaluationPassedValue, // Pass as int32
		curatedData.EvaluationType,
		curatedData.Feedback,
		metadataStr, // Ensure JSON string
		curatedData.SourceLogID,
		curatedData.CuratedBy,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for curated data: %w", err)
	}

	return tx.Commit()
}

// insertAgentPerformanceLog inserts an agent performance log into ClickHouse.
func insertAgentPerformanceLog(ctx context.Context, agentLog AgentPerformanceLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for agent performance log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			agent_id, agent_name, agent_type, task_id, task_type, status, started_at,
			completed_at, duration, success, error_message, input_tokens, output_tokens,
			cost, health_status, capabilities, current_workload, max_concurrency, last_active_at, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_AGENT_PERFORMANCE_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for agent performance log: %w", err)
	}
	defer stmt.Close()

	// Convert bool to int32 for ClickHouse UInt8
	successValue := int32(0)
	if agentLog.Success {
		successValue = 1
	}

	// Handle nullable fields
	var completedAt interface{}
	var duration interface{}
	if agentLog.CompletedAt != nil {
		completedAt = *agentLog.CompletedAt
	}
	if agentLog.Duration != nil {
		duration = *agentLog.Duration
	}

	// Ensure metadata is valid JSON
	metadataStr := agentLog.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		agentLog.AgentID,
		agentLog.AgentName,
		agentLog.AgentType,
		agentLog.TaskID,
		agentLog.TaskType,
		agentLog.Status,
		agentLog.StartedAt,
		completedAt,
		duration,
		successValue,
		agentLog.ErrorMessage,
		agentLog.InputTokens,
		agentLog.OutputTokens,
		agentLog.Cost,
		agentLog.HealthStatus,
		agentLog.Capabilities,
		agentLog.CurrentWorkload,
		agentLog.MaxConcurrency,
		agentLog.LastActiveAt,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for agent performance log: %w", err)
	}

	return tx.Commit()
}

// insertWorkflowExecutionLog inserts a workflow execution log into ClickHouse.
func insertWorkflowExecutionLog(ctx context.Context, workflowLog WorkflowExecutionLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for workflow execution log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			workflow_id, workflow_name, execution_id, status, started_at, completed_at,
			duration, tasks_total, tasks_completed, tasks_failed, agents_involved,
			total_cost, created_by, error_message, progress_percent, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?, ?, ?
		)
	`, CLICKHOUSE_WORKFLOW_EXECUTION_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for workflow execution log: %w", err)
	}
	defer stmt.Close()

	// Handle nullable fields
	var completedAt interface{}
	var duration interface{}
	if workflowLog.CompletedAt != nil {
		completedAt = *workflowLog.CompletedAt
	}
	if workflowLog.Duration != nil {
		duration = *workflowLog.Duration
	}

	// Ensure metadata is valid JSON
	metadataStr := workflowLog.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		workflowLog.WorkflowID,
		workflowLog.WorkflowName,
		workflowLog.ExecutionID,
		workflowLog.Status,
		workflowLog.StartedAt,
		completedAt,
		duration,
		workflowLog.TasksTotal,
		workflowLog.TasksCompleted,
		workflowLog.TasksFailed,
		workflowLog.AgentsInvolved,
		workflowLog.TotalCost,
		workflowLog.CreatedBy,
		workflowLog.ErrorMessage,
		workflowLog.ProgressPercent,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for workflow execution log: %w", err)
	}

	return tx.Commit()
}

// insertPlanningExecutionLog inserts a planning execution log into ClickHouse.
func insertPlanningExecutionLog(ctx context.Context, planningLog PlanningExecutionLog) error {
	tx, err := clickhouseConn.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for planning execution log: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (
			goal_id, goal_description, execution_id, status, started_at, completed_at,
			duration, tasks_total, tasks_completed, tasks_failed, total_cost,
			created_by, error_message, progress_percent, metadata
		) VALUES (
			?, ?, ?, ?, ?, ?,
			?, ?, ?, ?, ?,
			?, ?, ?, ?
		)
	`, CLICKHOUSE_PLANNING_LOGS_TABLE))
	if err != nil {
		return fmt.Errorf("failed to prepare insert statement for planning execution log: %w", err)
	}
	defer stmt.Close()

	// Handle nullable fields
	var completedAt interface{}
	var duration interface{}
	if planningLog.CompletedAt != nil {
		completedAt = *planningLog.CompletedAt
	}
	if planningLog.Duration != nil {
		duration = *planningLog.Duration
	}

	// Ensure metadata is valid JSON
	metadataStr := planningLog.Metadata
	if metadataStr == "" {
		metadataStr = "{}"
	}

	_, err = stmt.ExecContext(
		ctx,
		planningLog.GoalID,
		planningLog.GoalDescription,
		planningLog.ExecutionID,
		planningLog.Status,
		planningLog.StartedAt,
		completedAt,
		duration,
		planningLog.TasksTotal,
		planningLog.TasksCompleted,
		planningLog.TasksFailed,
		planningLog.TotalCost,
		planningLog.CreatedBy,
		planningLog.ErrorMessage,
		planningLog.ProgressPercent,
		metadataStr,
	)
	if err != nil {
		return fmt.Errorf("failed to execute insert statement for planning execution log: %w", err)
	}

	return tx.Commit()
}

// truncateString truncates a string to a max length for logging.
func truncateString(s string, maxLen int) string {
	if len(s) > maxLen {
		return s[:min(len(s), maxLen)] + "..."
	}
	return s
}

// --- Multi-Agent Kafka Consumers ---

// startAgentPerformanceConsumer reads agent performance messages from Kafka.
func startAgentPerformanceConsumer(ctx context.Context) {
	log.Printf("Starting Kafka agent performance consumer for topic: %s", kafkaAgentTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Agent performance consumer shutting down.")
			return
		default:
			m, err := kafkaAgentReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching agent performance Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var agentLog AgentPerformanceLog
			if err := json.Unmarshal(m.Value, &agentLog); err != nil {
				log.Printf("Error unmarshalling agent performance message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaAgentReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing agent performance message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertAgentPerformanceLog(ctx, agentLog); err != nil {
				log.Printf("Error inserting agent performance log: %v", err)
			} else {
				log.Printf("Successfully processed agent performance log for agent: %s", agentLog.AgentID)
			}

			if commitErr := kafkaAgentReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing agent performance message: %v", commitErr)
			}
		}
	}
}

// startWorkflowExecutionConsumer reads workflow execution messages from Kafka.
func startWorkflowExecutionConsumer(ctx context.Context) {
	log.Printf("Starting Kafka workflow execution consumer for topic: %s", kafkaWorkflowTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Workflow execution consumer shutting down.")
			return
		default:
			m, err := kafkaWorkflowReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching workflow execution Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var workflowLog WorkflowExecutionLog
			if err := json.Unmarshal(m.Value, &workflowLog); err != nil {
				log.Printf("Error unmarshalling workflow execution message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaWorkflowReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing workflow execution message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertWorkflowExecutionLog(ctx, workflowLog); err != nil {
				log.Printf("Error inserting workflow execution log: %v", err)
			} else {
				log.Printf("Successfully processed workflow execution log for workflow: %s", workflowLog.WorkflowID)
			}

			if commitErr := kafkaWorkflowReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing workflow execution message: %v", commitErr)
			}
		}
	}
}

// startPlanningExecutionConsumer reads planning execution messages from Kafka.
func startPlanningExecutionConsumer(ctx context.Context) {
	log.Printf("Starting Kafka planning execution consumer for topic: %s", kafkaPlanningTopic)
	for {
		select {
		case <-ctx.Done():
			log.Println("Planning execution consumer shutting down.")
			return
		default:
			m, err := kafkaPlanningReader.FetchMessage(ctx)
			if err != nil {
				if errors.Is(err, context.Canceled) {
					return
				}
				log.Printf("Error fetching planning execution Kafka message: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			var planningLog PlanningExecutionLog
			if err := json.Unmarshal(m.Value, &planningLog); err != nil {
				log.Printf("Error unmarshalling planning execution message: %v, message: %s", err, string(m.Value))
				if commitErr := kafkaPlanningReader.CommitMessages(ctx, m); commitErr != nil {
					log.Printf("Error committing planning execution message after unmarshalling failure: %v", commitErr)
				}
				continue
			}

			if err := insertPlanningExecutionLog(ctx, planningLog); err != nil {
				log.Printf("Error inserting planning execution log: %v", err)
			} else {
				log.Printf("Successfully processed planning execution log for goal: %s", planningLog.GoalID)
			}

			if commitErr := kafkaPlanningReader.CommitMessages(ctx, m); commitErr != nil {
				log.Printf("Error committing planning execution message: %v", commitErr)
			}
		}
	}
}

// --- Main Function ---

func main() {
	// Setup signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start Kafka consumers in goroutines
	go startKafkaConsumer(appCtx)
	go startAgentPerformanceConsumer(appCtx)
	go startWorkflowExecutionConsumer(appCtx)
	go startPlanningExecutionConsumer(appCtx)

	// Start synthetic data generation loop in a goroutine
	go startSyntheticDataGeneration(appCtx)

	// Wait for shutdown signal
	sig := <-sigChan
	log.Printf("Received signal: %v. Initiating graceful shutdown...", sig)
	appCancel()

	// Allow some time for goroutines to finish
	time.Sleep(2 * time.Second)

	// Cleanup resources
	if err := kafkaReader.Close(); err != nil {
		log.Printf("Error closing Kafka inference reader: %v", err)
	}
	if err := kafkaAgentReader.Close(); err != nil {
		log.Printf("Error closing Kafka agent reader: %v", err)
	}
	if err := kafkaWorkflowReader.Close(); err != nil {
		log.Printf("Error closing Kafka workflow reader: %v", err)
	}
	if err := kafkaPlanningReader.Close(); err != nil {
		log.Printf("Error closing Kafka planning reader: %v", err)
	}
	if err := clickhouseConn.Close(); err != nil {
		log.Printf("Error closing ClickHouse connection: %v", err)
	}
	if err := redisClient.Close(); err != nil {
		log.Printf("Error closing Redis client: %v", err)
	}
	log.Println("Shutdown complete.")
}
