package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"math" // Import math for NaN check
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync" // Import sync for mutex
	"time"

	_ "github.com/ClickHouse/clickhouse-go/v2" // ClickHouse driver
	"github.com/go-redis/redis/v8"             // Redis client
	"github.com/gorilla/mux"                   // For routing
)

// --- Constants ---
const (
	redisAddr = "redis:6379" // Assuming Redis address from env or default

	// ClickHouse constants from environment variables or defaults
	clickhouseHostEnv     = "CLICKHOUSE_HOST"
	clickhousePortEnv     = "CLICKHOUSE_PORT"
	clickhouseDBEnv       = "CLICKHOUSE_DB"
	clickhouseUserEnv     = "CLICKHOUSE_USER"
	clickhousePasswordEnv = "CLICKHOUSE_PASSWORD"

	CLICKHOUSE_INFERENCE_LOGS_TABLE     = "inference_logs"
	CLICKHOUSE_EVALUATION_RESULTS_TABLE = "llm_evaluation_results"
	CLICKHOUSE_CURATED_DATA_TABLE       = "curated_data"
	CLICKHOUSE_AGENT_PERFORMANCE_TABLE  = "agent_performance"
	CLICKHOUSE_WORKFLOW_EXECUTION_TABLE = "workflow_execution"
	CLICKHOUSE_PLANNING_LOGS_TABLE      = "planning_logs"

	clickhouseTimestampFormat = "2006-01-02 15:04:05.*********"

	REDIS_MODEL_PROFILES_KEY_PREFIX = "model_profile:"
	MODEL_PROFILE_UPDATES_CHANNEL   = "model_profile_updates"
)

// --- Structs (matching data-processor for consistency) ---

// InferenceLog struct is here to align with other services if needed for recent logs, etc.
type InferenceLog struct {
	RequestID           string          `json:"request_id"`
	Timestamp           time.Time       `json:"timestamp"`
	Method              string          `json:"method"`
	Path                string          `json:"path"`
	ClientIP            string          `json:"client_ip"`
	UserAgent           string          `json:"user_agent"`
	SelectedBackendID   string          `json:"selected_backend_id"`
	BackendURL          string          `json:"backend_url"`
	BackendType         string          `json:"backend_type,omitempty"`
	ResponseTimestamp   time.Time       `json:"response_timestamp"`
	LatencyMs           float64         `json:"latency_ms"`
	StatusCode          int32           `json:"status_code"`
	Error               string          `json:"error,omitempty"`
	PolicyIDApplied     string          `json:"policy_id_applied,omitempty"`
	ModelRequested      string          `json:"model_requested,omitempty"`
	ModelUsed           string          `json:"model_used,omitempty"`
	Stream              bool            `json:"stream,omitempty"`
	CPUUsage            float64         `json:"cpu_usage_rate"`
	MemoryUsage         float64         `json:"memory_usage_bytes"`
	TotalCost           float64         `json:"total_cost"`
	InputTokens         int64           `json:"input_tokens,omitempty"`
	OutputTokens        int64           `json:"output_tokens,omitempty"`
	TaskType            string          `json:"task_type,omitempty"`
	ConversationID      string          `json:"conversation_id,omitempty"`
	UserID              string          `json:"user_id,omitempty"`
	UserRoles           []string        `json:"user_roles,omitempty"`
	RequestHeaders      json.RawMessage `json:"request_headers,omitempty"`
	RequestBodySnippet  string          `json:"request_body_snippet,omitempty"`
	ResponseBodySnippet string          `json:"response_body_snippet,omitempty"`
	ResponseHeaders     json.RawMessage `json:"response_headers,omitempty"`
}

type ModelProfile struct {
	ID                  string          `json:"id"`
	Name                string          `json:"name"`
	Aliases             []string        `json:"aliases"`
	Capabilities        []string        `json:"capabilities"`
	PricingTier         string          `json:"pricing_tier"`
	DataSensitivity     string          `json:"data_sensitivity"`
	ExpectedLatencyMs   float64         `json:"expected_latency_ms"`
	ExpectedCost        float64         `json:"expected_cost"`
	BackendURL          string          `json:"url"`
	BackendType         string          `json:"backend_type"`
	CostPerInputToken   float64         `json:"cost_per_input_token"`
	CostPerOutputToken  float64         `json:"cost_per_output_token"`
	CPUCostPerHour      float64         `json:"cpu_cost_per_hour"`
	MemoryCostPerHour   float64         `json:"memory_cost_per_hour"`
	APIKey              string          `json:"api_key,omitempty"`
	CreatedAt           time.Time       `json:"created_at"`
	UpdatedAt           time.Time       `json:"updated_at"`
	Version             string          `json:"version,omitempty"`
	Owner               string          `json:"owner,omitempty"`
	Status              string          `json:"status,omitempty"`
	DocumentationURL    string          `json:"documentation_url,omitempty"`
	License             string          `json:"license,omitempty"`
	FineTuningDetails   string          `json:"fine_tuning_details,omitempty"`
	InputContextLength  int             `json:"input_context_length,omitempty"`
	OutputContextLength int             `json:"output_context_length,omitempty"`
	TrainingDataInfo    string          `json:"training_data_info,omitempty"`
	LastEvaluatedAt     *time.Time      `json:"last_evaluated_at,omitempty"`
	EvaluationMetrics   json.RawMessage `json:"evaluation_metrics,omitempty"`
	ComplianceTags      []string        `json:"compliance_tags,omitempty"`
	Region              string          `json:"region,omitempty"`
	Provider            string          `json:"provider,omitempty"`
}

// InferenceSummary represents aggregated metrics for the dashboard summary.
type InferenceSummary struct {
	TotalRequests     int64   `json:"total_requests"`
	AverageLatencyMs  float64 `json:"average_latency_ms"`
	TotalCost         float64 `json:"total_cost"`
	TotalInputTokens  int64   `json:"total_input_tokens"`
	TotalOutputTokens int64   `json:"total_output_tokens"`
	BackendBreakdown  []struct {
		BackendID      string  `json:"backend_id"`
		RequestCount   int64   `json:"request_count"`
		AverageLatency float64 `json:"average_latency"`
		TotalCost      float64 `json:"total_cost"`
	} `json:"backend_breakdown"`
}

type TimeSeriesData struct {
	TimeInterval      string  `json:"timeInterval"`
	RequestCount      int     `json:"requestCount"`
	AverageLatencyMs  float64 `json:"averageLatencyMs"`
	TotalInputTokens  int64   `json:"totalInputTokens"`
	TotalOutputTokens int64   `json:"totalOutputTokens"`
	TotalCost         float64 `json:"totalCost"`
	PolicyIDApplied   string  `json:"policyIdApplied"` // Will be empty for this API
	ModelUsed         string  `json:"modelUsed"`       // Will be empty for this API
}

type BackendLatency struct {
	BackendID        string    `json:"backendId"`
	AverageLatencyMs float64   `json:"averageLatencyMs"`
	LastUpdated      time.Time `json:"lastUpdated"`
}

type OptimalBackend struct {
	OptimalBackendID string    `json:"optimalBackendId"`
	AverageMetric    float64   `json:"averageMetric"` // Renamed from AverageLatencyMs to be more general
	Message          string    `json:"message"`
	Preference       string    `json:"preference"`
	Timestamp        time.Time `json:"timestamp"` // Added timestamp as per console logs
}

type Policy struct {
	ID           string          `json:"id"`
	Name         string          `json:"name"`
	Description  string          `json:"description"`
	Criteria     json.RawMessage `json:"criteria"`
	Action       string          `json:"action"`
	BackendID    string          `json:"backend_id,omitempty"`
	Priority     int             `json:"priority"`
	Rules        json.RawMessage `json:"rules,omitempty"`
	CreatedAt    time.Time       `json:"created_at"`
	UpdatedAt    time.Time       `json:"updated_at"`
	Metadata     json.RawMessage `json:"metadata,omitempty"`
	RateLimit    int             `json:"rate_limit,omitempty"`
	Budget       float64         `json:"budget,omitempty"`
	Effect       string          `json:"effect,omitempty"`
	Subjects     []string        `json:"subjects,omitempty"`
	ResourceType string          `json:"resource_type,omitempty"`
	ResourceIDs  []string        `json:"resource_ids,omitempty"`
	Permissions  []string        `json:"permissions,omitempty"`
}

type EvaluationResult struct {
	ID               string    `json:"id"`
	RequestID        string    `json:"request_id"`
	Prompt           string    `json:"prompt"`
	LLMResponse      string    `json:"llm_response"`
	ModelID          string    `json:"model_id"`
	TaskType         string    `json:"task_type,omitempty"`
	EvaluationType   string    `json:"evaluation_type"`
	Score            float64   `json:"score"`
	Passed           bool      `json:"passed"`
	Feedback         string    `json:"feedback,omitempty"`
	EvaluatedAt      time.Time `json:"evaluated_at"`
	ExpectedResponse string    `json:"expected_response,omitempty"`
	RawMetrics       string    `json:"raw_metrics"`
	Metadata         string    `json:"metadata"`
}

type CuratedData struct {
	ID               string    `json:"id"`
	RequestID        string    `json:"request_id"`
	Prompt           string    `json:"prompt"`
	LLMResponse      string    `json:"llm_response"`
	ModelID          string    `json:"model_id"`
	TaskType         string    `json:"task_type,omitempty"`
	GeneratedAt      time.Time `json:"generated_at"`
	EvaluationScore  float64   `json:"evaluation_score"`
	EvaluationPassed bool      `json:"evaluation_passed"`
	EvaluationType   string    `json:"evaluation_type"`
	Feedback         string    `json:"feedback,omitempty"`
	Metadata         string    `json:"metadata"`
	SourceLogID      string    `json:"source_log_id,omitempty"`
	CuratedBy        string    `json:"curated_by,omitempty"`
}

// Multi-Agent Orchestration Data Structures

type AgentPerformance struct {
	AgentID          string    `json:"agent_id"`
	AgentName        string    `json:"agent_name"`
	AgentType        string    `json:"agent_type"`
	TasksCompleted   int64     `json:"tasks_completed"`
	TasksSuccessful  int64     `json:"tasks_successful"`
	SuccessRate      float64   `json:"success_rate"`
	AverageLatencyMs float64   `json:"average_latency_ms"`
	TotalCost        float64   `json:"total_cost"`
	AverageCost      float64   `json:"average_cost"`
	LastActiveAt     time.Time `json:"last_active_at"`
	HealthStatus     string    `json:"health_status"`
	Capabilities     []string  `json:"capabilities"`
	CurrentWorkload  int       `json:"current_workload"`
	MaxConcurrency   int       `json:"max_concurrency"`
	UtilizationRate  float64   `json:"utilization_rate"`
}

type WorkflowExecution struct {
	WorkflowID      string     `json:"workflow_id"`
	WorkflowName    string     `json:"workflow_name"`
	Status          string     `json:"status"`
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	TasksTotal      int        `json:"tasks_total"`
	TasksCompleted  int        `json:"tasks_completed"`
	TasksFailed     int        `json:"tasks_failed"`
	AgentsInvolved  []string   `json:"agents_involved"`
	TotalCost       float64    `json:"total_cost"`
	CreatedBy       string     `json:"created_by"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	ProgressPercent float64    `json:"progress_percent"`
}

type PlanningExecution struct {
	GoalID          string     `json:"goal_id"`
	GoalDescription string     `json:"goal_description"`
	Status          string     `json:"status"`
	StartedAt       time.Time  `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at,omitempty"`
	Duration        *int64     `json:"duration_ms,omitempty"`
	TasksTotal      int        `json:"tasks_total"`
	TasksCompleted  int        `json:"tasks_completed"`
	TasksFailed     int        `json:"tasks_failed"`
	TotalCost       float64    `json:"total_cost"`
	CreatedBy       string     `json:"created_by"`
	ErrorMessage    string     `json:"error_message,omitempty"`
	ProgressPercent float64    `json:"progress_percent"`
}

type MultiAgentSummary struct {
	TotalAgents         int64               `json:"total_agents"`
	ActiveAgents        int64               `json:"active_agents"`
	TotalWorkflows      int64               `json:"total_workflows"`
	ActiveWorkflows     int64               `json:"active_workflows"`
	CompletedWorkflows  int64               `json:"completed_workflows"`
	FailedWorkflows     int64               `json:"failed_workflows"`
	TotalTasks          int64               `json:"total_tasks"`
	CompletedTasks      int64               `json:"completed_tasks"`
	AverageWorkflowTime float64             `json:"average_workflow_time_ms"`
	TotalCost           float64             `json:"total_cost"`
	AverageSuccessRate  float64             `json:"average_success_rate"`
	TopPerformingAgents []AgentPerformance  `json:"top_performing_agents"`
	RecentWorkflows     []WorkflowExecution `json:"recent_workflows"`
}

type CollaborationMetrics struct {
	AgentPairs           []AgentCollaboration `json:"agent_pairs"`
	MostUsedWorkflows    []WorkflowUsage      `json:"most_used_workflows"`
	AverageCollaboration float64              `json:"average_collaboration_score"`
	NetworkEfficiency    float64              `json:"network_efficiency"`
	CommunicationVolume  int64                `json:"communication_volume"`
}

type AgentCollaboration struct {
	Agent1ID           string  `json:"agent1_id"`
	Agent1Name         string  `json:"agent1_name"`
	Agent2ID           string  `json:"agent2_id"`
	Agent2Name         string  `json:"agent2_name"`
	CollaborationCount int64   `json:"collaboration_count"`
	SuccessRate        float64 `json:"success_rate"`
	AverageLatency     float64 `json:"average_latency_ms"`
	CollaborationScore float64 `json:"collaboration_score"`
}

type WorkflowUsage struct {
	WorkflowID   string  `json:"workflow_id"`
	WorkflowName string  `json:"workflow_name"`
	UsageCount   int64   `json:"usage_count"`
	SuccessRate  float64 `json:"success_rate"`
	AverageCost  float64 `json:"average_cost"`
	AverageTime  float64 `json:"average_time_ms"`
}

var (
	clickhouseConn *sql.DB
	redisClient    *redis.Client
	modelProfiles  map[string]ModelProfile
	modelProfileMu sync.RWMutex
)

// --- Initialization ---
func init() {
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	chHost := os.Getenv(clickhouseHostEnv)
	chPort := os.Getenv(clickhousePortEnv)
	chDB := os.Getenv(clickhouseDBEnv)
	chUser := os.Getenv(clickhouseUserEnv)
	chPassword := os.Getenv(clickhousePasswordEnv)

	if chHost == "" || chPort == "" || chDB == "" || chUser == "" || chPassword == "" {
		log.Fatalf("Missing one or more ClickHouse environment variables (CLICKHOUSE_HOST, CLICKHOUSE_PORT, CLICKHOUSE_DB, CLICKHOUSE_USER, CLICKHOUSE_PASSWORD)")
	}

	clickhouseDSN := fmt.Sprintf("tcp://%s:%s/%s?username=%s&password=%s",
		chHost, chPort, chDB, chUser, chPassword)

	var err error
	clickhouseConn, err = sql.Open("clickhouse", clickhouseDSN)
	if err != nil {
		log.Fatalf("Failed to open ClickHouse connection: %v", err)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err = clickhouseConn.PingContext(ctx); err != nil {
		log.Fatalf("Failed to ping ClickHouse with DSN '%s': %v", clickhouseDSN, err)
	}
	log.Println("Successfully connected to ClickHouse.")

	redisClient = redis.NewClient(&redis.Options{
		Addr: redisAddr,
		DB:   0,
	})
	ctxRedis, cancelRedis := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancelRedis()
	if _, err = redisClient.Ping(ctxRedis).Result(); err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	log.Println("Successfully connected to Redis.")

	if err = loadModelProfilesFromRedis(context.Background()); err != nil {
		log.Printf("Warning: Failed to load model profiles from Redis on startup: %v", err)
	} else {
		log.Printf("Loaded %d model profiles from Redis.", len(modelProfiles))
	}

	go setupRedisPubSub()
}

// --- Redis Pub/Sub ---
func setupRedisPubSub() {
	pubsub := redisClient.Subscribe(context.Background(), MODEL_PROFILE_UPDATES_CHANNEL)
	defer pubsub.Close()

	receiveCtx, receiveCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer receiveCancel()
	var err error // Declare err here
	if _, err = pubsub.Receive(receiveCtx); err != nil && err != context.DeadlineExceeded {
		log.Printf("Failed to subscribe to Redis Pub/Sub or timed out during initial receive: %v", err)
		return
	} else if err == context.DeadlineExceeded {
		log.Println("Timed out waiting for Redis Pub/Sub subscription confirmation, continuing...")
	}

	ch := pubsub.Channel()
	log.Printf("Subscribed to Redis Pub/Sub channel '%s'.", MODEL_PROFILE_UPDATES_CHANNEL)

	for msg := range ch {
		log.Printf("Received Redis Pub/Sub message: Channel=%s, Payload=%s", msg.Channel, msg.Payload)
		if msg.Channel == MODEL_PROFILE_UPDATES_CHANNEL {
			modelProfileMu.Lock()
			// Use the existing 'err' variable
			if err = loadModelProfilesFromRedis(context.Background()); err != nil {
				log.Printf("Error refreshing model profiles cache: %v", err)
			} else {
				log.Println("Model profiles cache refreshed due to pending updates...")
			}
			modelProfileMu.Unlock()
		}
	}
}

func loadModelProfilesFromRedis(ctx context.Context) error {
	newModelProfiles := make(map[string]ModelProfile)
	keys, err := redisClient.Keys(ctx, REDIS_MODEL_PROFILES_KEY_PREFIX+"*").Result()
	if err != nil && err != redis.Nil {
		return fmt.Errorf("failed to get model profile keys from Redis: %w", err)
	}
	for _, key := range keys {
		val, getErr := redisClient.Get(ctx, key).Result()
		if getErr != nil {
			log.Printf("Error getting model profile %s from Redis: %v", key, getErr)
			continue
		}
		var profile ModelProfile
		unmarshalErr := json.Unmarshal([]byte(val), &profile)
		if unmarshalErr != nil {
			log.Printf("Error unmarshalling model profile %s: %v", key, unmarshalErr)
			continue
		}
		newModelProfiles[profile.ID] = profile
	}
	modelProfileMu.Lock()
	modelProfiles = newModelProfiles
	modelProfileMu.Unlock()
	return nil
}

// --- Middleware ---
func loggingMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		log.Printf("Received request: %s %s", r.Method, r.URL.Path)
		next(w, r)
		log.Printf("Completed request: %s %s in %v", r.Method, r.URL.Path, time.Since(start))
	}
}

// --- Handlers ---

func apiOverview(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	if err := json.NewEncoder(w).Encode(map[string]string{"message": "Welcome to the Dashboard API!"}); err != nil {
		log.Printf("Error encoding API overview response: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	if err := json.NewEncoder(w).Encode(map[string]string{"status": "ok"}); err != nil {
		log.Printf("Error encoding health check response: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

func handleInferenceSummary(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	// Removed: queryValues := r.URL.Query()
	// Removed: startDateStr := queryValues.Get("startDate")
	// Removed: endDateStr := queryValues.Get("endDate")

	startTime, endTime, err := getTimeframeFromRequest(r) // Use the new helper
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	summaryQuery := fmt.Sprintf(`
		SELECT
			count() as total_requests,
			avg(latency_ms) as average_latency_ms,
			sum(total_cost) as total_cost,
			sum(input_tokens) as total_input_tokens,
			sum(output_tokens) as total_output_tokens
		FROM %s
		WHERE timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
	`, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat)) // Use startTime/endTime

	var summary InferenceSummary
	row := clickhouseConn.QueryRowContext(r.Context(), summaryQuery)
	var totalRequests sql.NullInt64
	var averageLatencyMs sql.NullFloat64
	var totalCost sql.NullFloat64
	var totalInputTokens sql.NullInt64
	var totalOutputTokens sql.NullInt64

	err = row.Scan(
		&totalRequests,
		&averageLatencyMs,
		&totalCost,
		&totalInputTokens,
		&totalOutputTokens,
	)
	if err != nil {
		log.Printf("Error querying ClickHouse for inference summary: %v", err)
		http.Error(w, "Error fetching summary data", http.StatusInternalServerError)
		return
	}

	// Safely assign values, handling potential NULL/NaN
	summary.TotalRequests = totalRequests.Int64
	if averageLatencyMs.Valid && !math.IsNaN(averageLatencyMs.Float64) {
		summary.AverageLatencyMs = averageLatencyMs.Float64
	} else {
		summary.AverageLatencyMs = 0 // Default to 0 if NaN or NULL
	}
	if totalCost.Valid && !math.IsNaN(totalCost.Float64) {
		summary.TotalCost = totalCost.Float64
	} else {
		summary.TotalCost = 0 // Default to 0 if NaN or NULL
	}
	summary.TotalInputTokens = totalInputTokens.Int64
	summary.TotalOutputTokens = totalOutputTokens.Int64

	// Fetch backend breakdown
	breakdownQuery := fmt.Sprintf(`
        SELECT
            selected_backend_id,
            count() AS request_count,
            avg(latency_ms) AS average_latency,
            sum(total_cost) AS total_cost
        FROM %s
        WHERE timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
        GROUP BY selected_backend_id
        ORDER BY request_count DESC
    `, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat)) // Use startTime/endTime

	rows, err := clickhouseConn.QueryContext(r.Context(), breakdownQuery)
	if err != nil {
		log.Printf("Error querying ClickHouse for backend breakdown: %v", err)
		// Don't fail the whole request, just proceed without breakdown
	} else {
		defer rows.Close()
		for rows.Next() {
			var backendBreakdown struct {
				BackendID      string  `json:"backend_id"`
				RequestCount   int64   `json:"request_count"`
				AverageLatency float64 `json:"average_latency"`
				TotalCost      float64 `json:"total_cost"`
			}
			var backendID sql.NullString
			var reqCount sql.NullInt64
			var avgLatency sql.NullFloat64
			var totalCostBreakdown sql.NullFloat64

			if err := rows.Scan(
				&backendID,
				&reqCount,
				&avgLatency,
				&totalCostBreakdown,
			); err != nil {
				log.Printf("Error scanning backend breakdown row: %v", err)
				continue
			}
			backendBreakdown.BackendID = backendID.String
			backendBreakdown.RequestCount = reqCount.Int64
			if avgLatency.Valid && !math.IsNaN(avgLatency.Float64) {
				backendBreakdown.AverageLatency = avgLatency.Float64
			} else {
				backendBreakdown.AverageLatency = 0
			}
			if totalCostBreakdown.Valid && !math.IsNaN(totalCostBreakdown.Float64) {
				backendBreakdown.TotalCost = totalCostBreakdown.Float64
			} else {
				backendBreakdown.TotalCost = 0
			}

			summary.BackendBreakdown = append(summary.BackendBreakdown, backendBreakdown)
		}
		if err := rows.Err(); err != nil {
			log.Printf("Error iterating backend breakdown rows: %v", err)
		}
	}

	log.Printf("Encoding inference summary. Total Requests: %d", summary.TotalRequests)
	if err := json.NewEncoder(w).Encode(summary); err != nil {
		log.Printf("Error encoding inference summary response: %v", err)
		http.Error(w, "Error encoding summary data", http.StatusInternalServerError)
	}
}

func handleTimeSeriesData(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	queryValues := r.URL.Query()
	timeframeStr := queryValues.Get("timeframe") // This one is actually used later in the switch statement

	startTime, endTime, err := getTimeframeFromRequest(r) // Use the new helper
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Determine bucket size based on timeframe for better visualization
	// This logic is duplicated from data-processor to keep dashboard-api self-contained
	// in case data-processor is not available or for direct ClickHouse access.
	var timeFn string
	switch timeframeStr { // Use timeframeStr directly
	case "1h":
		timeFn = "toStartOfMinute" // More granularity for 1h
	case "24h", "7d":
		timeFn = "toStartOfHour"
	case "30d", "90d", "all":
		timeFn = "toStartOfDay"
	default:
		timeFn = "toStartOfHour" // Default for unknown or short timeframe
	}

	// REVERTED QUERY: Only aggregate by time_interval, remove policy_id_applied and model_used
	query := fmt.Sprintf(`
		SELECT
			%s(timestamp) as time_interval,
			count() as request_count,
			avg(latency_ms) as average_latency_ms,
			sum(input_tokens) as total_input_tokens,
			sum(output_tokens) as total_output_tokens,
			sum(total_cost) as total_cost
		FROM %s
		WHERE timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
		GROUP BY time_interval
		ORDER BY time_interval ASC
	`, timeFn, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat)) // Use startTime/endTime

	rows, err := clickhouseConn.QueryContext(r.Context(), query)
	if err != nil {
		log.Printf("Error querying ClickHouse for time series data: %v", err)
		http.Error(w, "Error fetching time series data", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var timeSeries []TimeSeriesData
	for rows.Next() {
		var ts TimeSeriesData
		var timeInterval time.Time
		// REVERTED SCAN: Removed policyIdApplied and modelUsed
		var requestCount sql.NullInt64
		var averageLatencyMs sql.NullFloat64
		var totalInputTokens sql.NullInt64
		var totalOutputTokens sql.NullInt64
		var totalCost sql.NullFloat64

		if err := rows.Scan(
			&timeInterval,
			&requestCount,
			&averageLatencyMs,
			&totalInputTokens,
			&totalOutputTokens,
			&totalCost,
		); err != nil {
			log.Printf("Error scanning time series row: %v", err)
			continue
		}

		ts.TimeInterval = timeInterval.Format(time.RFC3339Nano)
		// PolicyIDApplied and ModelUsed will remain empty for this API response, as per your request
		ts.RequestCount = int(requestCount.Int64)
		if averageLatencyMs.Valid && !math.IsNaN(averageLatencyMs.Float64) {
			ts.AverageLatencyMs = averageLatencyMs.Float64
		} else {
			ts.AverageLatencyMs = 0
		}
		ts.TotalInputTokens = totalInputTokens.Int64
		ts.TotalOutputTokens = totalOutputTokens.Int64
		if totalCost.Valid && !math.IsNaN(totalCost.Float64) {
			ts.TotalCost = totalCost.Float64
		} else {
			ts.TotalCost = 0
		}

		timeSeries = append(timeSeries, ts)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating time series rows: %v", err)
		http.Error(w, "Error iterating time series rows", http.StatusInternalServerError)
		return
	}

	log.Printf("Encoding time series data. Count: %d", len(timeSeries))
	if err := json.NewEncoder(w).Encode(timeSeries); err != nil {
		log.Printf("Error encoding time series response: %v", err)
		http.Error(w, "Error encoding time series data", http.StatusInternalServerError)
	}
}

// NEW: getTimeframeFromRequest helper function (copied from data-processor for consistency)
func getTimeframeFromRequest(r *http.Request) (time.Time, time.Time, error) {
	timeframeStr := r.URL.Query().Get("timeframe")
	if timeframeStr == "" {
		// Default to last 24 hours if no timeframe is specified
		return time.Now().Add(-24 * time.Hour), time.Now(), nil
	}

	now := time.Now().UTC()
	var startTime time.Time

	switch timeframeStr {
	case "1h":
		startTime = now.Add(-1 * time.Hour)
	case "24h":
		startTime = now.Add(-24 * time.Hour)
	case "7d":
		startTime = now.Add(-7 * 24 * time.Hour)
	case "30d":
		startTime = now.Add(-30 * 24 * time.Hour)
	case "90d":
		startTime = now.Add(-90 * 24 * time.Hour)
	case "all":
		// For "all time", pick a very old date or rely on ClickHouse default behavior
		startTime = time.Time{} // Zero time value
	default:
		return time.Time{}, time.Time{}, fmt.Errorf("invalid timeframe: %s", timeframeStr)
	}

	return startTime, now, nil
}

func handleRecentInferenceLogs(w http.ResponseWriter, r *http.Request) {
	log.Println("handleRecentInferenceLogs called")
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type

	defer func() {
		if rec := recover(); rec != nil {
			log.Printf("Panic in handleRecentInferenceLogs: %v", rec)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
	}()

	startTime, endTime, err := getTimeframeFromRequest(r) // Use the new helper
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Corrected query to include 'stream' column and use correct CPU/Memory names
	// Order of SELECTed columns matches the scanning order to prevent issues
	query := fmt.Sprintf(`
        SELECT
            request_id, timestamp, method, path, client_ip, user_agent, selected_backend_id, backend_url, backend_type,
            response_timestamp, status_code, latency_ms, input_tokens, output_tokens, total_cost, error, policy_id_applied,
            model_requested, model_used, stream, cpu_usage_rate, memory_usage_bytes, task_type, conversation_id, user_id,
            user_roles, request_headers, request_body_snippet, response_body_snippet, response_headers
        FROM %s
        WHERE timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
        ORDER BY timestamp DESC
        LIMIT 20
    `, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat)) // Apply timeframe

	log.Printf("Executing ClickHouse query for recent logs: %s", query)

	rows, err := clickhouseConn.QueryContext(r.Context(), query)
	if err != nil {
		log.Printf("Error querying recent inference logs: %v", err)
		http.Error(w, "Error querying recent inference logs", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var logs []InferenceLog
	for rows.Next() {
		var logEntry InferenceLog
		var userRoles []string
		var requestHeaders, responseHeaders, requestBodySnippet, responseBodySnippet, errorStr, conversationID, userID, modelRequested, modelUsed, taskType sql.NullString
		var statusCode sql.NullInt32
		var latencyMs, cpuUsage, memoryUsage, totalCost sql.NullFloat64
		var inputTokens, outputTokens sql.NullInt64 // Use NullInt64 for int64 fields
		var stream uint8                            // Scan into uint8 for ClickHouse UInt8, then convert to bool

		if err := rows.Scan(
			&logEntry.RequestID,
			&logEntry.Timestamp,
			&logEntry.Method,
			&logEntry.Path,
			&logEntry.ClientIP,
			&logEntry.UserAgent,
			&logEntry.SelectedBackendID,
			&logEntry.BackendURL,
			&logEntry.BackendType,
			&logEntry.ResponseTimestamp,
			&statusCode,
			&latencyMs,
			&inputTokens,
			&outputTokens,
			&totalCost,
			&errorStr,
			&logEntry.PolicyIDApplied,
			&modelRequested,
			&modelUsed, // Scan into local variable
			&stream,    // Scan the stream column into uint8
			&cpuUsage,
			&memoryUsage,
			&taskType,
			&conversationID,
			&userID,
			&userRoles,
			&requestHeaders,
			&requestBodySnippet,
			&responseBodySnippet,
			&responseHeaders,
		); err != nil {
			log.Printf("Error scanning recent log row: %v", err)
			continue
		}

		// Assign values from sql.Null* to struct fields, handling nulls
		logEntry.StatusCode = statusCode.Int32
		if latencyMs.Valid {
			logEntry.LatencyMs = latencyMs.Float64
		} else {
			logEntry.LatencyMs = 0
		}
		if inputTokens.Valid {
			logEntry.InputTokens = inputTokens.Int64
		} else {
			logEntry.InputTokens = 0
		}
		if outputTokens.Valid {
			logEntry.OutputTokens = outputTokens.Int64
		} else {
			logEntry.OutputTokens = 0
		}
		if totalCost.Valid {
			logEntry.TotalCost = totalCost.Float64
		} else {
			logEntry.TotalCost = 0
		}
		logEntry.Error = errorStr.String
		// PolicyIDApplied is already scanned directly into logEntry.PolicyIDApplied
		logEntry.ModelRequested = modelRequested.String
		logEntry.ModelUsed = modelUsed.String // Assign from local variable
		logEntry.Stream = stream == 1         // Convert uint8 to bool
		if cpuUsage.Valid {
			logEntry.CPUUsage = cpuUsage.Float64
		} else {
			logEntry.CPUUsage = 0
		}
		if memoryUsage.Valid {
			logEntry.MemoryUsage = memoryUsage.Float64
		} else {
			logEntry.MemoryUsage = 0
		}
		logEntry.TaskType = taskType.String
		logEntry.ConversationID = conversationID.String
		logEntry.UserID = userID.String
		logEntry.UserRoles = userRoles // Already scanned directly as []string

		if requestHeaders.Valid {
			logEntry.RequestHeaders = json.RawMessage(requestHeaders.String)
		} else {
			logEntry.RequestHeaders = json.RawMessage(`{}`)
		}
		if requestBodySnippet.Valid {
			logEntry.RequestBodySnippet = requestBodySnippet.String
		} else {
			logEntry.RequestBodySnippet = ""
		}
		if responseHeaders.Valid {
			logEntry.ResponseHeaders = json.RawMessage(responseHeaders.String)
		} else {
			logEntry.ResponseHeaders = json.RawMessage(`{}`)
		}
		if responseBodySnippet.Valid {
			logEntry.ResponseBodySnippet = responseBodySnippet.String
		} else {
			logEntry.ResponseBodySnippet = ""
		}

		logs = append(logs, logEntry)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Rows error in handleRecentInferenceLogs: %v", err)
		http.Error(w, "Error reading recent inference logs", http.StatusInternalServerError)
		return
	}

	log.Printf("Returning %d recent logs", len(logs))

	if err := json.NewEncoder(w).Encode(logs); err != nil {
		log.Printf("Error encoding recent logs response: %v", err)
		http.Error(w, "Error encoding recent logs data", http.StatusInternalServerError)
	}
}

func handleBackendLatencies(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	// Removed: timeframeStr := r.URL.Query().Get("timeframe") // Add timeframe parameter
	startTime, endTime, err := getTimeframeFromRequest(r) // Use the new helper
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	query := fmt.Sprintf(`
		SELECT
			selected_backend_id,
			avg(latency_ms) as average_latency_ms,
			max(timestamp) as last_updated
		FROM %s
		WHERE latency_ms IS NOT NULL AND latency_ms > 0 AND timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
		GROUP BY selected_backend_id
		ORDER BY average_latency_ms ASC
	`, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat))

	rows, err := clickhouseConn.QueryContext(r.Context(), query)
	if err != nil {
		log.Printf("Error querying ClickHouse for backend latencies: %v", err)
		http.Error(w, "Error fetching backend latencies", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var latencies []BackendLatency
	for rows.Next() {
		var l BackendLatency
		var avgLatency sql.NullFloat64
		var lastUpdated sql.NullTime
		if err := rows.Scan(&l.BackendID, &avgLatency, &lastUpdated); err != nil {
			log.Printf("Error scanning backend latency row: %v", err)
			continue
		}
		if avgLatency.Valid && !math.IsNaN(avgLatency.Float64) {
			l.AverageLatencyMs = avgLatency.Float64
		} else {
			l.AverageLatencyMs = 0
		}
		if lastUpdated.Valid {
			l.LastUpdated = lastUpdated.Time
		} else {
			l.LastUpdated = time.Time{}
		}
		latencies = append(latencies, l)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating backend latencies rows: %v", err)
		http.Error(w, "Error iterating backend latencies rows", http.StatusInternalServerError)
		return
	}

	if err := json.NewEncoder(w).Encode(latencies); err != nil {
		log.Printf("Error encoding backend latencies response: %v", err)
		http.Error(w, "Error encoding backend latencies", http.StatusInternalServerError)
	}
}

func handleOptimalBackend(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type

	// Get timeframe from request
	startTime, endTime, err := getTimeframeFromRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	preference := r.URL.Query().Get("preference")
	if preference == "" {
		var p string
		if p, err = redisClient.Get(r.Context(), "routing:optimization_preference").Result(); err == redis.Nil { // Use routing:optimization_preference
			preference = "LATENCY"
			log.Println("No optimization preference found in Redis, defaulting to LATENCY.")
		} else if err != nil {
			log.Printf("Error fetching optimization preference from Redis: %v", err)
			http.Error(w, "Error fetching optimization preference", http.StatusInternalServerError)
			return
		} else {
			preference = p
		}
	}

	var query string
	var optimalMetric float64
	switch preference {
	case "COST":
		query = fmt.Sprintf(`
			SELECT
				selected_backend_id,
				avg(total_cost) as average_cost
			FROM %s
			WHERE total_cost IS NOT NULL AND total_cost >= 0 AND timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
			GROUP BY selected_backend_id
			ORDER BY average_cost ASC
			LIMIT 1
		`, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat))
		log.Printf("Executing ClickHouse query for optimal backend (COST): %s", query)
	case "LATENCY", "BALANCED":
		query = fmt.Sprintf(`
			SELECT
				selected_backend_id,
				avg(latency_ms) as average_latency_ms
			FROM %s
			WHERE latency_ms IS NOT NULL AND latency_ms > 0 AND timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
			GROUP BY selected_backend_id
			ORDER BY average_latency_ms ASC
			LIMIT 1
		`, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat))
		log.Printf("Executing ClickHouse query for optimal backend (LATENCY/BALANCED): %s", query)
	default:
		preference = "LATENCY" // Default for unknown
		query = fmt.Sprintf(`
			SELECT
				selected_backend_id,
				avg(latency_ms) as average_latency_ms
			FROM %s
			WHERE latency_ms IS NOT NULL AND latency_ms > 0 AND timestamp >= toDateTime64('%s', 9) AND timestamp <= toDateTime64('%s', 9)
			GROUP BY selected_backend_id
			ORDER BY average_latency_ms ASC
			LIMIT 1
		`, CLICKHOUSE_INFERENCE_LOGS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat))
		log.Printf("Executing ClickHouse query for optimal backend (DEFAULT -> LATENCY): %s", query)
	}

	row := clickhouseConn.QueryRowContext(r.Context(), query)
	var backendID sql.NullString
	var metricValue sql.NullFloat64
	err = row.Scan(&backendID, &metricValue)
	if err == sql.ErrNoRows {
		log.Println("No data found for optimal backend from ClickHouse. Attempting to get from Redis as a fallback.")
		// Attempt to get from Redis as a fallback
		optimalBackendData, redisErr := redisClient.Get(r.Context(), "routing:optimal_backend_data").Result()
		if redisErr == nil {
			var data struct {
				ID               string    `json:"id"`
				AverageMetric    float64   `json:"average_metric"` // CORRECTED: Matches JSON from ai-optimizer
				OptimizationType string    `json:"optimization_type"`
				Timestamp        time.Time `json:"timestamp"`
				Message          string    `json:"message"`
			}
			if unmarshalErr := json.Unmarshal([]byte(optimalBackendData), &data); unmarshalErr == nil {
				response := OptimalBackend{
					OptimalBackendID: data.ID,
					AverageMetric:    data.AverageMetric, // CORRECTED: Use the correct field
					Message:          data.Message,
					Preference:       data.OptimizationType, // Use OptimizationType from Redis
					Timestamp:        data.Timestamp,
				}
				log.Printf("Returning optimal backend from Redis fallback: %v", response)
				if err := json.NewEncoder(w).Encode(response); err != nil {
					log.Printf("Error encoding Redis fallback optimal backend response: %v", err)
					http.Error(w, "Internal server error", http.StatusInternalServerError)
				}
				return
			} else {
				log.Printf("Error unmarshalling Redis fallback optimal backend data: %v", unmarshalErr)
			}
		} else if redisErr != redis.Nil {
			log.Printf("Error getting optimal backend data from Redis: %v", redisErr)
		} else {
			log.Println("No optimal backend data found in Redis fallback.")
		}

		// If no rows from ClickHouse and no data from Redis fallback
		if err := json.NewEncoder(w).Encode(OptimalBackend{Message: "No optimal backend identified (no data available).", Preference: preference, Timestamp: time.Now()}); err != nil {
			log.Printf("Error encoding empty optimal backend response: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}
	if err != nil {
		log.Printf("Error querying ClickHouse for optimal backend: %v", err)
		http.Error(w, "Error fetching optimal backend", http.StatusInternalServerError)
		return
	}

	if metricValue.Valid && !math.IsNaN(metricValue.Float64) {
		optimalMetric = metricValue.Float64
	} else {
		optimalMetric = 0
	}

	response := OptimalBackend{
		OptimalBackendID: backendID.String,
		Message:          fmt.Sprintf("Optimal backend identified: %s with metric value of %.2f", backendID.String, optimalMetric),
		Preference:       preference,
		AverageMetric:    optimalMetric, // Assign metric to the general field
		Timestamp:        time.Now(),    // Add current timestamp
	}
	log.Printf("Returning optimal backend from ClickHouse query: %v", response)

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding optimal backend response: %v", err)
		http.Error(w, "Error encoding optimal backend data", http.StatusInternalServerError)
	}
}

func setOptimizationPreference(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	var requestBody struct {
		Preference string `json:"preference"`
	}
	if err := json.NewDecoder(r.Body).Decode(&requestBody); err != nil {
		log.Printf("Invalid request body for setOptimizationPreference: %v", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}
	validPreferences := map[string]bool{"COST": true, "LATENCY": true, "BALANCED": true}
	if !validPreferences[requestBody.Preference] {
		log.Printf("Invalid preference for setOptimizationPreference: %s", requestBody.Preference)
		http.Error(w, "Invalid preference. Must be 'COST', 'LATENCY', or 'BALANCED'.", http.StatusBadRequest)
		return
	}
	ctx, cancel := context.WithTimeout(r.Context(), 5*time.Second)
	defer cancel()
	// Changed from "ai_optimizer_preference" to "routing:optimization_preference" to match data-processor
	err := redisClient.Set(ctx, "routing:optimization_preference", requestBody.Preference, 0).Err()
	if err != nil {
		log.Printf("Error setting optimization preference in Redis: %v", err)
		http.Error(w, "Error setting optimization preference", http.StatusInternalServerError)
		return
	}
	log.Printf("Optimization preference set to: %s", requestBody.Preference)

	// Refresh the optimal backend immediately after setting preference
	// This makes the UI update quicker with the new preference effect
	q := r.URL.Query()
	q.Set("preference", requestBody.Preference)
	r.URL.RawQuery = q.Encode()
	// Re-calling handleOptimalBackend to return the updated optimal backend based on new preference
	handleOptimalBackend(w, r)
}

func proxyToPolicyManager(w http.ResponseWriter, r *http.Request, path string) {
	policyManagerURL := fmt.Sprintf("http://policy-manager:8083%s", path)
	log.Printf("Proxying request to policy manager: %s", policyManagerURL)

	proxyReq, err := http.NewRequest(r.Method, policyManagerURL, r.Body)
	if err != nil {
		log.Printf("Error creating proxy request to policy manager: %v", err)
		http.Error(w, "Internal Server Error", http.StatusInternalServerError)
		return
	}
	for name, values := range r.Header {
		for _, value := range values {
			if strings.ToLower(name) == "host" || strings.ToLower(name) == "connection" {
				continue
			}
			proxyReq.Header.Add(name, value)
		}
	}
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(proxyReq)
	if err != nil {
		log.Printf("Error proxying request to policy manager (URL: %s): %v", policyManagerURL, err)
		if r.Method == "GET" {
			switch path {
			case "/api/policies", "/api/policies/":
				provideMockPolicies(w, r)
			case "/api/model-profiles", "/api/model-profiles/", "/api/model_profiles", "/api/model_profiles/":
				provideMockModelProfiles(w, r)
			default:
				http.Error(w, "Service Unavailable or Unhandled Proxy Path", http.StatusServiceUnavailable)
			}
		} else {
			http.Error(w, "Policy Manager Service Unavailable for Write Operation", http.StatusServiceUnavailable)
		}
		return
	}
	defer resp.Body.Close()
	for name, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(name, value)
		}
	}
	w.WriteHeader(resp.StatusCode)
	if _, err := io.Copy(w, resp.Body); err != nil {
		log.Printf("Error copying response body from policy manager: %v", err)
	}
}

func provideMockPolicies(w http.ResponseWriter, _ *http.Request) {
	log.Printf("Providing mock policies data")
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	mockPolicies := []Policy{
		{
			ID:          "cost-optimization",
			Name:        "Cost Optimization",
			Description: "Routes requests to the most cost-effective backend that meets latency requirements",
			Criteria:    json.RawMessage(`[{"field": "model_requested", "operator": "=", "value": "cost-optimized"}]`),
			Action:      "OPTIMIZE",
			BackendID:   "",
			Priority:    10,
			Rules:       json.RawMessage(`{"task_type": "factual_query"}`),
			CreatedAt:   time.Now().Add(-48 * time.Hour),
			UpdatedAt:   time.Now().Add(-24 * time.Hour),
		},
		{
			ID:          "latency-optimization",
			Name:        "Latency Optimization",
			Description: "Routes requests to the fastest backend regardless of cost",
			Criteria:    json.RawMessage(`[{"field": "model_requested", "operator": "=", "value": "fast-response"}]`),
			Action:      "OPTIMIZE",
			BackendID:   "",
			Priority:    20,
			Rules:       json.RawMessage(`{"task_type": "code_generation"}`),
			CreatedAt:   time.Now().Add(-72 * time.Hour),
			UpdatedAt:   time.Now().Add(-12 * time.Hour),
		},
		{
			ID:          "default-routing",
			Name:        "Default Routing",
			Description: "Catch-all policy for unmatched requests",
			Criteria:    json.RawMessage(`[]`),
			Action:      "ROUTE",
			BackendID:   "default-backend-id",
			Priority:    100,
			Rules:       json.RawMessage(`{}`),
			CreatedAt:   time.Now().Add(-96 * time.Hour),
			UpdatedAt:   time.Now().Add(-96 * time.Hour),
		},
	}
	if err := json.NewEncoder(w).Encode(mockPolicies); err != nil {
		log.Printf("Error encoding mock policies: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

func provideMockModelProfiles(w http.ResponseWriter, _ *http.Request) {
	log.Printf("Providing mock model profiles data")
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	mockProfiles := []ModelProfile{
		{
			ID:                 "gpt-4o-mini",
			Name:               "GPT-4o Mini (External)",
			Aliases:            []string{"gpt4o-mini", "openai-mini"},
			Capabilities:       []string{"chat", "text-generation", "code-generation"},
			PricingTier:        "standard",
			DataSensitivity:    "low",
			ExpectedLatencyMs:  180,
			ExpectedCost:       0.00000015,
			BackendURL:         "https://api.openai.com/v1/chat/completions",
			BackendType:        "openai-external",
			CostPerInputToken:  0.00000015,
			CostPerOutputToken: 0.0000006,
			CreatedAt:          time.Now().Add(-48 * time.Hour),
			UpdatedAt:          time.Now().Add(-24 * time.Hour),
		},
		{
			ID:                 "gemini-pro",
			Name:               "Gemini 1.0 Pro (External)",
			Aliases:            []string{"gemini", "google-pro"},
			Capabilities:       []string{"chat", "text-generation", "multi-modal"},
			PricingTier:        "standard",
			DataSensitivity:    "low",
			ExpectedLatencyMs:  250,
			ExpectedCost:       0.000000125,
			BackendURL:         "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
			BackendType:        "google-external",
			CostPerInputToken:  0.000000125,
			CostPerOutputToken: 0.000000375,
			CreatedAt:          time.Now().Add(-72 * time.Hour),
			UpdatedAt:          time.Now().Add(-12 * time.Hour),
		},
		{
			ID:                 "claude-3-haiku",
			Name:               "Claude 3 Haiku (External)",
			Aliases:            []string{"claude-haiku", "anthropic-haiku"},
			Capabilities:       []string{"chat", "text-generation", "summarization"},
			PricingTier:        "standard",
			DataSensitivity:    "low",
			ExpectedLatencyMs:  300,
			ExpectedCost:       0.00000025,
			BackendURL:         "https://api.anthropic.com/v1/messages",
			BackendType:        "anthropic-external",
			CostPerInputToken:  0.00000025,
			CostPerOutputToken: 0.00000125,
			CreatedAt:          time.Now().Add(-96 * time.Hour),
			UpdatedAt:          time.Now().Add(-96 * time.Hour),
		},
		{
			ID:                 "default-backend-id",
			Name:               "Default Mock Backend GPU1",
			Aliases:            []string{"default-mock"},
			Capabilities:       []string{"chat"},
			PricingTier:        "free",
			DataSensitivity:    "low",
			ExpectedLatencyMs:  50,
			ExpectedCost:       0.0,
			BackendURL:         "http://mock-backend-gpu1:5001/v1/chat/completions",
			BackendType:        "openai",
			CostPerInputToken:  0.0,
			CostPerOutputToken: 0.0,
			CPUCostPerHour:     0.0,
			MemoryCostPerHour:  0.0,
			CreatedAt:          time.Now().Add(-24 * time.Hour),
			UpdatedAt:          time.Now().Add(-24 * time.Hour),
		},
	}
	if err := json.NewEncoder(w).Encode(mockProfiles); err != nil {
		log.Printf("Error encoding mock profiles: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

func handlePolicies(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-User-ID, X-User-Roles")
		w.WriteHeader(http.StatusOK)
		return
	}
	proxyToPolicyManager(w, r, r.URL.Path)
}

func handleModelProfiles(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-User-ID, X-User-Roles")
		w.WriteHeader(http.StatusOK)
		return
	}
	proxyToPolicyManager(w, r, r.URL.Path)
}

func handleEvaluationResults(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	queryValues := r.URL.Query()

	startTime, endTime, err := getTimeframeFromRequest(r) // Use the new helper
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	limitStr := queryValues.Get("limit")
	limit := 50
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	query := fmt.Sprintf(`
		SELECT
			id, request_id, prompt, llm_response, model_id, task_type,
			evaluation_type, score, passed, feedback, evaluated_at,
			expected_response, raw_metrics, metadata
		FROM %s
		WHERE evaluated_at >= toDateTime64('%s', 9) AND evaluated_at <= toDateTime64('%s', 9)
		ORDER BY evaluated_at DESC
		LIMIT %d
	`, CLICKHOUSE_EVALUATION_RESULTS_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat), limit)

	log.Printf("Executing ClickHouse query for evaluation results: %s", query)
	rows, err := clickhouseConn.QueryContext(r.Context(), query)
	if err != nil {
		log.Printf("Error fetching evaluation results: %v", err)
		http.Error(w, "Error fetching evaluation results", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var results []EvaluationResult
	for rows.Next() {
		var res EvaluationResult
		var feedback sql.NullString
		var expectedResponse sql.NullString
		var rawMetrics sql.NullString
		var metadata sql.NullString
		var taskType sql.NullString
		var passed uint8 // Scan into uint8, then convert to bool

		if err := rows.Scan(
			&res.ID, &res.RequestID, &res.Prompt, &res.LLMResponse, &res.ModelID, &taskType,
			&res.EvaluationType, &res.Score, &passed, &feedback, &res.EvaluatedAt,
			&expectedResponse, &rawMetrics, &metadata,
		); err != nil {
			log.Printf("Error scanning evaluation result row: %v", err)
			continue
		}
		res.TaskType = taskType.String
		res.Feedback = feedback.String
		res.ExpectedResponse = expectedResponse.String
		res.RawMetrics = rawMetrics.String
		res.Metadata = metadata.String
		res.Passed = passed == 1 // Convert uint8 to bool

		results = append(results, res)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating evaluation results rows: %v", err)
		// Instead of HTTP error, just return empty results, as this is for the dashboard
		// http.Error(w, "Error iterating evaluation results rows", http.StatusInternalServerError)
		// return
	}

	if err := json.NewEncoder(w).Encode(results); err != nil {
		log.Printf("Error encoding evaluation results response: %v", err)
		http.Error(w, "Error encoding evaluation results data", http.StatusInternalServerError)
	}
}

func handleCuratedData(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json") // Ensure JSON content type
	queryValues := r.URL.Query()

	startTime, endTime, err := getTimeframeFromRequest(r) // Use the new helper
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	limitStr := queryValues.Get("limit")
	limit := 50
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}
	query := fmt.Sprintf(`
		SELECT
			id, request_id, prompt, llm_response, model_id, task_type, generated_at,
			evaluation_score, evaluation_passed, evaluation_type, feedback, metadata, source_log_id, curated_by
		FROM %s
		WHERE generated_at >= toDateTime64('%s', 9) AND generated_at <= toDateTime64('%s', 9)
		ORDER BY generated_at DESC
		LIMIT %d
	`, CLICKHOUSE_CURATED_DATA_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat), limit)

	log.Printf("Executing ClickHouse query for curated data: %s", query)
	rows, err := clickhouseConn.QueryContext(r.Context(), query)
	if err != nil {
		log.Printf("Error fetching curated data: %v", err)
		http.Error(w, "Error fetching curated data", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	var data []CuratedData
	for rows.Next() {
		var entry CuratedData
		var feedback sql.NullString
		var metadata sql.NullString
		var sourceLogID sql.NullString
		var curatedBy sql.NullString
		var requestID sql.NullString
		var taskType sql.NullString
		var evaluationType sql.NullString
		var evaluationPassed uint8 // Scan into uint8, then convert to bool

		if err := rows.Scan(
			&entry.ID, &requestID, &entry.Prompt, &entry.LLMResponse, &entry.ModelID, &taskType, &entry.GeneratedAt,
			&entry.EvaluationScore, &evaluationPassed, &evaluationType, &feedback, &metadata, &sourceLogID, &curatedBy,
		); err != nil {
			log.Printf("Error scanning curated data row: %v", err)
			continue
		}

		entry.RequestID = requestID.String
		entry.TaskType = taskType.String
		entry.EvaluationType = evaluationType.String
		entry.Feedback = feedback.String
		entry.Metadata = metadata.String
		entry.SourceLogID = sourceLogID.String
		entry.CuratedBy = curatedBy.String
		entry.EvaluationPassed = evaluationPassed == 1 // Convert uint8 to bool

		data = append(data, entry)
	}

	if err := rows.Err(); err != nil {
		log.Printf("Error iterating curated data rows: %v", err)
		// Instead of HTTP error, just return empty results
		// http.Error(w, "Error iterating curated data rows", http.StatusInternalServerError)
		// return
	}

	if err := json.NewEncoder(w).Encode(data); err != nil {
		log.Printf("Error encoding curated data response: %v", err)
		http.Error(w, "Error encoding curated data", http.StatusInternalServerError)
	}
}

// --- Multi-Agent Orchestration Handlers ---

func handleMultiAgentSummary(w http.ResponseWriter, r *http.Request) {
	log.Println("Handling multi-agent summary request")

	startTime, endTime, err := getTimeframeFromRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Query agent performance summary
	agentQuery := fmt.Sprintf(`
		SELECT
			COUNT(DISTINCT agent_id) as total_agents,
			COUNT(DISTINCT CASE WHEN health_status = 'active' THEN agent_id END) as active_agents
		FROM %s
		WHERE last_active_at >= toDateTime64('%s', 9) AND last_active_at <= toDateTime64('%s', 9)
	`, CLICKHOUSE_AGENT_PERFORMANCE_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat))

	// Query workflow summary
	workflowQuery := fmt.Sprintf(`
		SELECT
			COUNT(*) as total_workflows,
			COUNT(CASE WHEN status = 'running' THEN 1 END) as active_workflows,
			COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_workflows,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_workflows,
			SUM(tasks_total) as total_tasks,
			SUM(tasks_completed) as completed_tasks,
			AVG(duration) as average_workflow_time,
			SUM(total_cost) as total_cost
		FROM %s
		WHERE started_at >= toDateTime64('%s', 9) AND started_at <= toDateTime64('%s', 9)
	`, CLICKHOUSE_WORKFLOW_EXECUTION_TABLE, startTime.Format(clickhouseTimestampFormat), endTime.Format(clickhouseTimestampFormat))

	var summary MultiAgentSummary

	// Execute agent query
	if err := clickhouseConn.QueryRow(agentQuery).Scan(&summary.TotalAgents, &summary.ActiveAgents); err != nil {
		log.Printf("Error querying agent summary: %v", err)
		// Set default values
		summary.TotalAgents = 0
		summary.ActiveAgents = 0
	}

	// Execute workflow query
	if err := clickhouseConn.QueryRow(workflowQuery).Scan(
		&summary.TotalWorkflows, &summary.ActiveWorkflows, &summary.CompletedWorkflows,
		&summary.FailedWorkflows, &summary.TotalTasks, &summary.CompletedTasks,
		&summary.AverageWorkflowTime, &summary.TotalCost); err != nil {
		log.Printf("Error querying workflow summary: %v", err)
		// Set default values
		summary.TotalWorkflows = 0
		summary.ActiveWorkflows = 0
		summary.CompletedWorkflows = 0
		summary.FailedWorkflows = 0
		summary.TotalTasks = 0
		summary.CompletedTasks = 0
		summary.AverageWorkflowTime = 0
		summary.TotalCost = 0
	}

	// Calculate success rate
	if summary.TotalWorkflows > 0 {
		summary.AverageSuccessRate = float64(summary.CompletedWorkflows) / float64(summary.TotalWorkflows)
	}

	// Get top performing agents (mock data for now)
	summary.TopPerformingAgents = []AgentPerformance{
		{
			AgentID:          "agent-1",
			AgentName:        "Data Analyst Pro",
			AgentType:        "data_analyst",
			TasksCompleted:   150,
			TasksSuccessful:  145,
			SuccessRate:      0.97,
			AverageLatencyMs: 250.0,
			TotalCost:        45.50,
			AverageCost:      0.30,
			HealthStatus:     "active",
			UtilizationRate:  0.75,
		},
	}

	// Get recent workflows (mock data for now)
	summary.RecentWorkflows = []WorkflowExecution{
		{
			WorkflowID:      "workflow-1",
			WorkflowName:    "Customer Analysis Pipeline",
			Status:          "completed",
			TasksTotal:      5,
			TasksCompleted:  5,
			TasksFailed:     0,
			TotalCost:       2.50,
			ProgressPercent: 100.0,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(summary); err != nil {
		log.Printf("Error encoding multi-agent summary: %v", err)
		http.Error(w, "Error encoding multi-agent summary", http.StatusInternalServerError)
	}
}

func handleAgentPerformance(w http.ResponseWriter, r *http.Request) {
	log.Println("Handling agent performance request")

	startTime, endTime, err := getTimeframeFromRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	queryValues := r.URL.Query()
	limitStr := queryValues.Get("limit")
	limit := 50
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// Use startTime and endTime for actual querying in production
	_ = startTime
	_ = endTime
	_ = limit

	// Mock data for now - in production this would query the agent_performance table
	agents := []AgentPerformance{
		{
			AgentID:          "agent-1",
			AgentName:        "Data Analyst Pro",
			AgentType:        "data_analyst",
			TasksCompleted:   150,
			TasksSuccessful:  145,
			SuccessRate:      0.97,
			AverageLatencyMs: 250.0,
			TotalCost:        45.50,
			AverageCost:      0.30,
			LastActiveAt:     time.Now().Add(-1 * time.Hour),
			HealthStatus:     "active",
			Capabilities:     []string{"statistical_analysis", "data_visualization"},
			CurrentWorkload:  3,
			MaxConcurrency:   5,
			UtilizationRate:  0.60,
		},
		{
			AgentID:          "agent-2",
			AgentName:        "Content Writer Expert",
			AgentType:        "content_writer",
			TasksCompleted:   89,
			TasksSuccessful:  87,
			SuccessRate:      0.98,
			AverageLatencyMs: 180.0,
			TotalCost:        32.10,
			AverageCost:      0.36,
			LastActiveAt:     time.Now().Add(-30 * time.Minute),
			HealthStatus:     "active",
			Capabilities:     []string{"content_writing", "copywriting", "editing"},
			CurrentWorkload:  2,
			MaxConcurrency:   4,
			UtilizationRate:  0.50,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(agents); err != nil {
		log.Printf("Error encoding agent performance: %v", err)
		http.Error(w, "Error encoding agent performance", http.StatusInternalServerError)
	}
}

func handleWorkflowExecutions(w http.ResponseWriter, r *http.Request) {
	log.Println("Handling workflow executions request")

	startTime, endTime, err := getTimeframeFromRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Use startTime and endTime for actual querying in production
	_ = startTime
	_ = endTime

	// Mock data for now - in production this would query the workflow_execution table
	workflows := []WorkflowExecution{
		{
			WorkflowID:      "workflow-1",
			WorkflowName:    "Customer Analysis Pipeline",
			Status:          "completed",
			StartedAt:       time.Now().Add(-2 * time.Hour),
			CompletedAt:     &[]time.Time{time.Now().Add(-1 * time.Hour)}[0],
			Duration:        &[]int64{3600000}[0], // 1 hour in ms
			TasksTotal:      5,
			TasksCompleted:  5,
			TasksFailed:     0,
			AgentsInvolved:  []string{"agent-1", "agent-2"},
			TotalCost:       2.50,
			CreatedBy:       "user-123",
			ProgressPercent: 100.0,
		},
		{
			WorkflowID:      "workflow-2",
			WorkflowName:    "Content Generation Workflow",
			Status:          "running",
			StartedAt:       time.Now().Add(-30 * time.Minute),
			TasksTotal:      3,
			TasksCompleted:  2,
			TasksFailed:     0,
			AgentsInvolved:  []string{"agent-2", "agent-3"},
			TotalCost:       1.20,
			CreatedBy:       "user-456",
			ProgressPercent: 66.7,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(workflows); err != nil {
		log.Printf("Error encoding workflow executions: %v", err)
		http.Error(w, "Error encoding workflow executions", http.StatusInternalServerError)
	}
}

func handleCollaborationMetrics(w http.ResponseWriter, r *http.Request) {
	log.Println("Handling collaboration metrics request")

	startTime, endTime, err := getTimeframeFromRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Use startTime and endTime for actual querying in production
	_ = startTime
	_ = endTime

	// Mock data for now
	metrics := CollaborationMetrics{
		AgentPairs: []AgentCollaboration{
			{
				Agent1ID:           "agent-1",
				Agent1Name:         "Data Analyst Pro",
				Agent2ID:           "agent-2",
				Agent2Name:         "Content Writer Expert",
				CollaborationCount: 25,
				SuccessRate:        0.96,
				AverageLatency:     450.0,
				CollaborationScore: 0.92,
			},
		},
		MostUsedWorkflows: []WorkflowUsage{
			{
				WorkflowID:   "workflow-template-1",
				WorkflowName: "Data Analysis Pipeline",
				UsageCount:   45,
				SuccessRate:  0.94,
				AverageCost:  2.30,
				AverageTime:  1800000, // 30 minutes in ms
			},
		},
		AverageCollaboration: 0.89,
		NetworkEfficiency:    0.87,
		CommunicationVolume:  1250,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(metrics); err != nil {
		log.Printf("Error encoding collaboration metrics: %v", err)
		http.Error(w, "Error encoding collaboration metrics", http.StatusInternalServerError)
	}
}

func handlePlanningExecutions(w http.ResponseWriter, r *http.Request) {
	log.Println("Handling planning executions request")

	startTime, endTime, err := getTimeframeFromRequest(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Use startTime and endTime for actual querying in production
	_ = startTime
	_ = endTime

	// Mock data for now - in production this would query the planning_logs table
	executions := []PlanningExecution{
		{
			GoalID:          "goal-1",
			GoalDescription: "Analyze customer support tickets and generate insights",
			Status:          "completed",
			StartedAt:       time.Now().Add(-3 * time.Hour),
			CompletedAt:     &[]time.Time{time.Now().Add(-2 * time.Hour)}[0],
			Duration:        &[]int64{3600000}[0], // 1 hour in ms
			TasksTotal:      4,
			TasksCompleted:  4,
			TasksFailed:     0,
			TotalCost:       3.20,
			CreatedBy:       "user-789",
			ProgressPercent: 100.0,
		},
		{
			GoalID:          "goal-2",
			GoalDescription: "Create comprehensive market analysis report",
			Status:          "running",
			StartedAt:       time.Now().Add(-45 * time.Minute),
			TasksTotal:      6,
			TasksCompleted:  3,
			TasksFailed:     0,
			TotalCost:       1.80,
			CreatedBy:       "user-101",
			ProgressPercent: 50.0,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(executions); err != nil {
		log.Printf("Error encoding planning executions: %v", err)
		http.Error(w, "Error encoding planning executions", http.StatusInternalServerError)
	}
}

// --- Main ---
func main() {
	defer clickhouseConn.Close()
	defer redisClient.Close()

	router := mux.NewRouter()

	// Applying logging middleware to all relevant routes
	router.HandleFunc("/api/overview", loggingMiddleware(apiOverview)).Methods("GET")
	router.HandleFunc("/healthz", loggingMiddleware(healthCheckHandler)).Methods("GET")
	router.HandleFunc("/api/summary", loggingMiddleware(handleInferenceSummary)).Methods("GET")
	router.HandleFunc("/api/time-series", loggingMiddleware(handleTimeSeriesData)).Methods("GET")
	router.HandleFunc("/api/inference-logs", loggingMiddleware(handleRecentInferenceLogs)).Methods("GET")
	router.HandleFunc("/api/backend-latencies", loggingMiddleware(handleBackendLatencies)).Methods("GET")
	router.HandleFunc("/api/optimal-backend", loggingMiddleware(handleOptimalBackend)).Methods("GET")
	router.HandleFunc("/api/set-preference", loggingMiddleware(setOptimizationPreference)).Methods("POST")
	// Policies and Model Profiles endpoints, proxied to policy-manager
	router.HandleFunc("/api/policies", loggingMiddleware(handlePolicies)).Methods("GET", "OPTIONS")
	router.HandleFunc("/api/policies/", loggingMiddleware(handlePolicies)).Methods("GET", "OPTIONS") // Handles trailing slash
	router.HandleFunc("/api/model-profiles", loggingMiddleware(handleModelProfiles)).Methods("GET", "OPTIONS")
	router.HandleFunc("/api/model-profiles/", loggingMiddleware(handleModelProfiles)).Methods("GET", "OPTIONS") // Handles trailing slash
	router.HandleFunc("/api/model_profiles", loggingMiddleware(handleModelProfiles)).Methods("GET", "OPTIONS")  // Alternative path
	router.HandleFunc("/api/model_profiles/", loggingMiddleware(handleModelProfiles)).Methods("GET", "OPTIONS") // Alternative path, trailing slash
	router.HandleFunc("/api/evaluation-results", loggingMiddleware(handleEvaluationResults)).Methods("GET")
	router.HandleFunc("/api/curated-data", loggingMiddleware(handleCuratedData)).Methods("GET")

	// Multi-Agent Orchestration endpoints
	router.HandleFunc("/api/multi-agent/summary", loggingMiddleware(handleMultiAgentSummary)).Methods("GET")
	router.HandleFunc("/api/multi-agent/agents", loggingMiddleware(handleAgentPerformance)).Methods("GET")
	router.HandleFunc("/api/multi-agent/workflows", loggingMiddleware(handleWorkflowExecutions)).Methods("GET")
	router.HandleFunc("/api/multi-agent/collaboration", loggingMiddleware(handleCollaborationMetrics)).Methods("GET")
	router.HandleFunc("/api/multi-agent/planning", loggingMiddleware(handlePlanningExecutions)).Methods("GET")

	// CORS middleware
	corsHandler := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, X-LLM-API-Key, X-Preferred-LLM-ID, X-Conversation-ID, X-User-ID, X-User-Roles")
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
			next.ServeHTTP(w, r)
		})
	}
	router.Use(corsHandler)

	log.Println("Starting dashboard API server on :8081")
	if err := http.ListenAndServe(":8081", router); err != nil {
		log.Fatalf("Error starting server: %v", err)
	}
}
