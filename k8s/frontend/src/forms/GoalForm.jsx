import React, { useState, useCallback } from 'react';
import { Plus, Trash2, Calendar, Target, AlertTriangle } from 'lucide-react';
import {
  GOAL_PRIORITIES,
  CONSTRAINT_TYPES,
  CONSTRAINT_OPERATORS,
  CONSTRAINT_SEVERITIES,
  SUCCESS_CRITERIA_OPERATORS
} from '../utils/constants';
import {
  DEFAULT_GOAL_REQUEST,
  DEFAULT_SUCCESS_CRITERION,
  DEFAULT_CONSTRAINT
} from '../types/planning';

const GoalForm = ({ 
  isOpen, 
  onClose, 
  onSubmit, 
  initialData = null,
  isLoading = false 
}) => {
  const [formData, setFormData] = useState(() => {
    const data = {
      ...DEFAULT_GOAL_REQUEST,
      ...initialData
    };

    // Ensure success criteria have IDs
    if (data.success_criteria) {
      data.success_criteria = data.success_criteria.map(criterion => ({
        ...criterion,
        id: criterion.id || `criterion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }));
    }

    // Ensure constraints have IDs
    if (data.constraints) {
      data.constraints = data.constraints.map(constraint => ({
        ...constraint,
        id: constraint.id || `constraint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }));
    }

    return data;
  });

  const [errors, setErrors] = useState({});

  // Handle basic field changes
  const handleFieldChange = useCallback((field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  }, [errors]);

  // Handle success criteria changes
  const handleSuccessCriteriaChange = useCallback((index, field, value) => {
    setFormData(prev => ({
      ...prev,
      success_criteria: prev.success_criteria.map((criterion, i) =>
        i === index ? { ...criterion, [field]: value } : criterion
      )
    }));
  }, []);

  const addSuccessCriterion = useCallback(() => {
    setFormData(prev => ({
      ...prev,
      success_criteria: [...prev.success_criteria, {
        ...DEFAULT_SUCCESS_CRITERION,
        id: `criterion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }]
    }));
  }, []);

  const removeSuccessCriterion = useCallback((index) => {
    setFormData(prev => ({
      ...prev,
      success_criteria: prev.success_criteria.filter((_, i) => i !== index)
    }));
  }, []);

  // Handle constraints changes
  const handleConstraintChange = useCallback((index, field, value) => {
    setFormData(prev => ({
      ...prev,
      constraints: prev.constraints.map((constraint, i) =>
        i === index ? { ...constraint, [field]: value } : constraint
      )
    }));
  }, []);

  const addConstraint = useCallback(() => {
    setFormData(prev => ({
      ...prev,
      constraints: [...prev.constraints, {
        ...DEFAULT_CONSTRAINT,
        id: `constraint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }]
    }));
  }, []);

  const removeConstraint = useCallback((index) => {
    setFormData(prev => ({
      ...prev,
      constraints: prev.constraints.filter((_, i) => i !== index)
    }));
  }, []);

  // Form validation
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (!formData.description.trim()) {
      newErrors.description = 'Goal description is required';
    } else if (formData.description.trim().length < 10) {
      newErrors.description = 'Goal description must be at least 10 characters';
    } else if (formData.description.trim().length > 5000) {
      newErrors.description = 'Goal description must be less than 5000 characters';
    }

    if (formData.priority < 1 || formData.priority > 10) {
      newErrors.priority = 'Priority must be between 1 and 10';
    }

    // Validate success criteria
    formData.success_criteria.forEach((criterion, index) => {
      if (!criterion.description.trim()) {
        newErrors[`success_criteria_${index}_description`] = 'Description is required';
      }
      if (!criterion.metric.trim()) {
        newErrors[`success_criteria_${index}_metric`] = 'Metric is required';
      }
      if (!criterion.target) {
        newErrors[`success_criteria_${index}_target`] = 'Target value is required';
      }
      if (criterion.weight < 0 || criterion.weight > 1) {
        newErrors[`success_criteria_${index}_weight`] = 'Weight must be between 0 and 1';
      }
    });

    // Validate constraints
    formData.constraints.forEach((constraint, index) => {
      if (!constraint.description.trim()) {
        newErrors[`constraint_${index}_description`] = 'Description is required';
      }
      if (!constraint.limit.trim()) {
        newErrors[`constraint_${index}_limit`] = 'Limit value is required';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Clean up the data before submission
    let deadline = null;
    if (formData.deadline) {
      try {
        deadline = new Date(formData.deadline).toISOString();
      } catch (error) {
        console.error('Invalid deadline format:', formData.deadline);
        deadline = null;
      }
    }

    const cleanedData = {
      ...formData,
      deadline,
      success_criteria: formData.success_criteria
        .filter(sc => sc.description.trim() && sc.metric.trim() && sc.target)
        .map(sc => ({
          ...sc,
          description: sc.description.trim().replace(/[""]/g, '"'), // Normalize quotes
          metric: sc.metric.trim(),
          target: sc.target.toString().trim()
        })),
      constraints: formData.constraints
        .filter(c => c.description.trim() && c.limit.trim())
        .map(c => ({
          ...c,
          description: c.description.trim().replace(/[""]/g, '"'), // Normalize quotes
          limit: c.limit.toString().trim()
        }))
    };

    onSubmit(cleanedData);
  }, [formData, validateForm, onSubmit]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Target className="mr-2" size={24} />
            {initialData ? 'Edit Goal' : 'Create New Goal'}
          </h2>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">Basic Information</h3>
            
            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Goal Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Describe what you want to achieve..."
                rows={3}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Priority and Deadline */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => handleFieldChange('priority', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  {GOAL_PRIORITIES.map(priority => (
                    <option key={priority.value} value={priority.value}>
                      {priority.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Deadline (Optional)
                </label>
                <input
                  type="datetime-local"
                  value={formData.deadline || ''}
                  onChange={(e) => handleFieldChange('deadline', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
          </div>

          {/* Success Criteria */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800">Success Criteria</h3>
              <button
                type="button"
                onClick={addSuccessCriterion}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Plus size={16} className="mr-1" />
                Add Criterion
              </button>
            </div>

            {formData.success_criteria.map((criterion, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-700">Criterion {index + 1}</h4>
                  <button
                    type="button"
                    onClick={() => removeSuccessCriterion(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Description *
                    </label>
                    <input
                      type="text"
                      value={criterion.description}
                      onChange={(e) => handleSuccessCriteriaChange(index, 'description', e.target.value)}
                      placeholder="What should be achieved?"
                      className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 ${
                        errors[`success_criteria_${index}_description`] ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Metric *
                    </label>
                    <input
                      type="text"
                      value={criterion.metric}
                      onChange={(e) => handleSuccessCriteriaChange(index, 'metric', e.target.value)}
                      placeholder="e.g., accuracy, count, percentage"
                      className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 ${
                        errors[`success_criteria_${index}_metric`] ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Operator
                    </label>
                    <select
                      value={criterion.operator}
                      onChange={(e) => handleSuccessCriteriaChange(index, 'operator', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                    >
                      {SUCCESS_CRITERIA_OPERATORS.map(op => (
                        <option key={op} value={op}>{op}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Target Value *
                    </label>
                    <input
                      type="text"
                      value={criterion.target}
                      onChange={(e) => handleSuccessCriteriaChange(index, 'target', e.target.value)}
                      placeholder="Target value"
                      className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 ${
                        errors[`success_criteria_${index}_target`] ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Weight (0.0-1.0)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="1"
                      step="0.1"
                      value={criterion.weight}
                      onChange={(e) => handleSuccessCriteriaChange(index, 'weight', parseFloat(e.target.value))}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={`required-${index}`}
                      checked={criterion.required}
                      onChange={(e) => handleSuccessCriteriaChange(index, 'required', e.target.checked)}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                    <label htmlFor={`required-${index}`} className="ml-2 text-xs font-medium text-gray-600">
                      Required
                    </label>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Constraints */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800">Constraints</h3>
              <button
                type="button"
                onClick={addConstraint}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Plus size={16} className="mr-1" />
                Add Constraint
              </button>
            </div>

            {formData.constraints.map((constraint, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-700">Constraint {index + 1}</h4>
                  <button
                    type="button"
                    onClick={() => removeConstraint(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Type
                    </label>
                    <select
                      value={constraint.type}
                      onChange={(e) => handleConstraintChange(index, 'type', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                    >
                      {CONSTRAINT_TYPES.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Description *
                    </label>
                    <input
                      type="text"
                      value={constraint.description}
                      onChange={(e) => handleConstraintChange(index, 'description', e.target.value)}
                      placeholder="Describe the constraint"
                      className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 ${
                        errors[`constraint_${index}_description`] ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Operator
                    </label>
                    <select
                      value={constraint.operator}
                      onChange={(e) => handleConstraintChange(index, 'operator', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                    >
                      {CONSTRAINT_OPERATORS.map(op => (
                        <option key={op} value={op}>{op}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Limit *
                    </label>
                    <input
                      type="text"
                      value={constraint.limit}
                      onChange={(e) => handleConstraintChange(index, 'limit', e.target.value)}
                      placeholder="e.g., 2h, $100, 95%"
                      className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-indigo-500 ${
                        errors[`constraint_${index}_limit`] ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">
                      Severity
                    </label>
                    <select
                      value={constraint.severity}
                      onChange={(e) => handleConstraintChange(index, 'severity', e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                    >
                      {CONSTRAINT_SEVERITIES.map(severity => (
                        <option key={severity} value={severity}>{severity}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Creating...' : (initialData ? 'Update Goal' : 'Create Goal')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GoalForm;
