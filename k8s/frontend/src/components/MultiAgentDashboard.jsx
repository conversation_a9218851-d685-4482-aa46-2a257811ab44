import React, { useState, useEffect, useCallback } from 'react';
import {
  Users,
  Network,
  Activity,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  MessageSquare,
  Settings,
  Plus,
  Eye,
  Play,
  Pause,
  Square,
  BarChart3
} from 'lucide-react';
import { MetricCard } from './DashboardCards';
import AgentRegistryView from './AgentRegistryView';
import WorkflowDesignerView from './WorkflowDesignerView';
import MultiAgentAnalytics from './MultiAgentAnalytics';

const MultiAgentDashboard = ({
  showNotification
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [agents, setAgents] = useState([]);
  const [workflows, setWorkflows] = useState([]);
  const [executions, setExecutions] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch data from multi-agent orchestrator
  const fetchAgents = useCallback(async () => {
    try {
      const response = await fetch('/api/multi-agent/v1/agents');
      if (response.ok) {
        const data = await response.json();
        setAgents(data);
      }
    } catch (error) {
      console.error('Failed to fetch agents:', error);
      showNotification('Failed to fetch agents', 'error');
    }
  }, [showNotification]);

  const fetchWorkflows = useCallback(async () => {
    try {
      const response = await fetch('/api/multi-agent/v1/workflows');
      if (response.ok) {
        const data = await response.json();
        setWorkflows(data);
      }
    } catch (error) {
      console.error('Failed to fetch workflows:', error);
      showNotification('Failed to fetch workflows', 'error');
    }
  }, [showNotification]);

  const fetchAnalytics = useCallback(async () => {
    try {
      const [agentAnalytics, workflowAnalytics, collaborationAnalytics] = await Promise.all([
        fetch('/api/multi-agent/v1/analytics/agents').then(r => r.ok ? r.json() : {}),
        fetch('/api/multi-agent/v1/analytics/workflows').then(r => r.ok ? r.json() : {}),
        fetch('/api/multi-agent/v1/analytics/collaboration').then(r => r.ok ? r.json() : {})
      ]);

      setAnalytics({
        agents: agentAnalytics,
        workflows: workflowAnalytics,
        collaboration: collaborationAnalytics
      });
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    }
  }, []);

  const fetchAllData = useCallback(async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchAgents(),
        fetchWorkflows(),
        fetchAnalytics()
      ]);
    } finally {
      setLoading(false);
    }
  }, [fetchAgents, fetchWorkflows, fetchAnalytics]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchAllData();
    setRefreshing(false);
    showNotification('Data refreshed successfully', 'success');
  };

  useEffect(() => {
    fetchAllData();
    
    // Set up polling for real-time updates
    const interval = setInterval(fetchAllData, 10000); // 10 seconds
    
    return () => clearInterval(interval);
  }, [fetchAllData]);

  // Calculate overview metrics
  const overviewMetrics = {
    totalAgents: agents.length,
    activeAgents: agents.filter(a => a.status === 'idle' || a.status === 'busy').length,
    totalWorkflows: workflows.length,
    activeWorkflows: workflows.filter(w => w.status === 'executing').length,
    completedWorkflows: workflows.filter(w => w.status === 'completed').length,
    averageCollaboration: analytics?.collaboration?.average_score || 0,
    totalMessages: analytics?.collaboration?.total_messages || 0,
    efficiency: analytics?.workflows?.average_efficiency || 0
  };

  const TabButton = ({ name, icon, label, count }) => (
    <button
      onClick={() => setActiveTab(name)}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
        activeTab === name
          ? 'bg-indigo-600 text-white'
          : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      {icon}
      <span className="font-medium">{label}</span>
      {count !== undefined && (
        <span className={`px-2 py-1 text-xs rounded-full ${
          activeTab === name ? 'bg-indigo-500' : 'bg-gray-200 text-gray-600'
        }`}>
          {count}
        </span>
      )}
    </button>
  );

  const AgentStatusBadge = ({ status }) => {
    const colors = {
      idle: 'bg-green-100 text-green-800',
      busy: 'bg-yellow-100 text-yellow-800',
      offline: 'bg-red-100 text-red-800',
      maintenance: 'bg-blue-100 text-blue-800',
      error: 'bg-red-100 text-red-800'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  const WorkflowStatusBadge = ({ status }) => {
    const colors = {
      draft: 'bg-gray-100 text-gray-800',
      active: 'bg-blue-100 text-blue-800',
      executing: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      paused: 'bg-orange-100 text-orange-800',
      cancelled: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[status] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Multi-Agent Orchestration</h2>
          <p className="text-gray-600 mt-1">Coordinate and monitor AI agent collaboration</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {refreshing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Refreshing...
              </>
            ) : (
              <>
                <Activity size={16} className="mr-2" />
                Refresh
              </>
            )}
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-2 border-b border-gray-200">
        <TabButton 
          name="overview" 
          icon={<BarChart3 size={20} />} 
          label="Overview" 
        />
        <TabButton 
          name="agents" 
          icon={<Users size={20} />} 
          label="Agents" 
          count={overviewMetrics.activeAgents}
        />
        <TabButton 
          name="workflows" 
          icon={<Network size={20} />} 
          label="Workflows" 
          count={overviewMetrics.activeWorkflows}
        />
        <TabButton 
          name="analytics" 
          icon={<TrendingUp size={20} />} 
          label="Analytics" 
        />
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              icon={<Users size={24} />}
              title="Active Agents"
              value={`${overviewMetrics.activeAgents}/${overviewMetrics.totalAgents}`}
              description="Agents ready for work"
              color="blue"
            />
            <MetricCard
              icon={<Network size={24} />}
              title="Active Workflows"
              value={overviewMetrics.activeWorkflows}
              description="Currently executing"
              color="yellow"
            />
            <MetricCard
              icon={<CheckCircle size={24} />}
              title="Completed"
              value={overviewMetrics.completedWorkflows}
              description="Successfully finished"
              color="green"
            />
            <MetricCard
              icon={<Zap size={24} />}
              title="Efficiency"
              value={`${(overviewMetrics.efficiency * 100).toFixed(1)}%`}
              description="Average workflow efficiency"
              color="purple"
            />
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Agents */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Agents</h3>
              <div className="space-y-3">
                {agents.slice(0, 5).map((agent) => (
                  <div key={agent.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                        <Users size={16} className="text-indigo-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{agent.name}</p>
                        <p className="text-sm text-gray-500">{agent.type}</p>
                      </div>
                    </div>
                    <AgentStatusBadge status={agent.status} />
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Workflows */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Workflows</h3>
              <div className="space-y-3">
                {workflows.slice(0, 5).map((workflow) => (
                  <div key={workflow.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <Network size={16} className="text-green-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{workflow.name}</p>
                        <p className="text-sm text-gray-500">{workflow.agents?.length || 0} agents</p>
                      </div>
                    </div>
                    <WorkflowStatusBadge status={workflow.status} />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Agents Tab */}
      {activeTab === 'agents' && (
        <AgentRegistryView 
          agents={agents}
          onRefresh={fetchAgents}
          showNotification={showNotification}
        />
      )}

      {/* Workflows Tab */}
      {activeTab === 'workflows' && (
        <WorkflowDesignerView 
          workflows={workflows}
          agents={agents}
          onRefresh={fetchWorkflows}
          showNotification={showNotification}
        />
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <MultiAgentAnalytics 
          analytics={analytics}
          agents={agents}
          workflows={workflows}
          showNotification={showNotification}
        />
      )}
    </div>
  );
};

export default MultiAgentDashboard;
