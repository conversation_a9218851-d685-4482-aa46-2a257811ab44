import React, { useState, useEffect, useCallback } from 'react';
import {
  Target,
  Play,
  Pause,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  BarChart3,
  Activity,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { getGoalStatusColor, getTaskStatusColor, GoalStatus } from '../types/planning';
import GoalForm from '../forms/GoalForm';
import GoalDetailView from './GoalDetailView';
import PlanningCostInsights from './PlanningCostInsights';

const PlanningDashboard = ({
  goals = [],
  goalsLoading,
  planningMetrics,
  onCreateGoal,
  onUpdateGoal,
  onDeleteGoal,
  onViewGoal,
  onExecuteGoal,
  onGeneratePlan,
  showNotification,
  // Real-time monitoring props
  isMonitoring = false,
  monitoringGoals = [],
  onStartMonitoring,
  onStopMonitoring
}) => {
  const [showGoalForm, setShowGoalForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState(null);
  const [selectedGoal, setSelectedGoal] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  // Handle goal creation
  const handleCreateGoal = useCallback(async (goalData) => {
    setFormLoading(true);
    try {
      await onCreateGoal(goalData);
      setShowGoalForm(false);
    } catch (error) {
      console.error('Error creating goal:', error);
    } finally {
      setFormLoading(false);
    }
  }, [onCreateGoal]);

  // Handle goal editing
  const handleEditGoal = useCallback((goal) => {
    setEditingGoal(goal);
    setShowGoalForm(true);
  }, []);

  const handleUpdateGoal = useCallback(async (goalData) => {
    if (!editingGoal) return;
    
    setFormLoading(true);
    try {
      await onUpdateGoal(editingGoal.id, goalData);
      setShowGoalForm(false);
      setEditingGoal(null);
    } catch (error) {
      console.error('Error updating goal:', error);
    } finally {
      setFormLoading(false);
    }
  }, [editingGoal, onUpdateGoal]);

  // Handle form close
  const handleFormClose = useCallback(() => {
    setShowGoalForm(false);
    setEditingGoal(null);
  }, []);

  // Handle goal actions
  const handleExecuteGoal = useCallback(async (goalId) => {
    try {
      await onExecuteGoal(goalId);
    } catch (error) {
      console.error('Error executing goal:', error);
    }
  }, [onExecuteGoal]);

  const handleGeneratePlan = useCallback(async (goalId) => {
    try {
      await onGeneratePlan(goalId);
    } catch (error) {
      console.error('Error generating plan:', error);
    }
  }, [onGeneratePlan]);

  const handleDeleteGoal = useCallback(async (goalId) => {
    if (window.confirm('Are you sure you want to delete this goal? This action cannot be undone.')) {
      try {
        await onDeleteGoal(goalId);
      } catch (error) {
        console.error('Error deleting goal:', error);
      }
    }
  }, [onDeleteGoal]);

  // Metrics cards
  const MetricCard = ({ icon, title, value, description, color = 'indigo' }) => (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex items-center">
        <div className={`p-3 rounded-lg bg-${color}-100 text-${color}-600 mr-4`}>
          {icon}
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
    </div>
  );

  // Goal status badge
  const StatusBadge = ({ status }) => (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getGoalStatusColor(status)}`}>
      {status}
    </span>
  );

  // Goal priority badge
  const PriorityBadge = ({ priority }) => {
    const getPriorityColor = (p) => {
      if (p >= 9) return 'bg-red-100 text-red-800';
      if (p >= 7) return 'bg-orange-100 text-orange-800';
      if (p >= 4) return 'bg-yellow-100 text-yellow-800';
      return 'bg-green-100 text-green-800';
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(priority)}`}>
        Priority {priority}
      </span>
    );
  };

  // Goal action buttons
  const GoalActions = ({ goal }) => {
    const canExecute = goal.status === GoalStatus.READY;
    const canGeneratePlan = goal.status === GoalStatus.PENDING;
    const isActive = [GoalStatus.PLANNING, GoalStatus.EXECUTING].includes(goal.status);

    return (
      <div className="flex items-center space-x-2">
        <button
          onClick={() => onViewGoal(goal.id)}
          className="p-2 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors"
          title="View Details"
        >
          <Eye size={16} />
        </button>
        
        {canGeneratePlan && (
          <button
            onClick={() => handleGeneratePlan(goal.id)}
            className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
            title="Generate Plan"
          >
            <BarChart3 size={16} />
          </button>
        )}
        
        {canExecute && (
          <button
            onClick={() => handleExecuteGoal(goal.id)}
            className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-colors"
            title="Execute Goal"
          >
            <Play size={16} />
          </button>
        )}
        
        {!isActive && (
          <>
            <button
              onClick={() => handleEditGoal(goal)}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors"
              title="Edit Goal"
            >
              <Edit size={16} />
            </button>
            
            <button
              onClick={() => handleDeleteGoal(goal.id)}
              className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete Goal"
            >
              <Trash2 size={16} />
            </button>
          </>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">Planning Dashboard</h2>
          {isMonitoring && monitoringGoals.length > 0 && (
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <div className="flex items-center mr-4">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                <span>Real-time monitoring active</span>
              </div>
              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                {monitoringGoals.length} goal{monitoringGoals.length !== 1 ? 's' : ''} monitored
              </span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-3">
          {/* Monitoring toggle */}
          {goals.some(g => [GoalStatus.PLANNING, GoalStatus.EXECUTING].includes(g.status)) && (
            <button
              onClick={isMonitoring ? onStopMonitoring : onStartMonitoring}
              className={`inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md ${
                isMonitoring
                  ? 'text-red-700 bg-red-100 hover:bg-red-200'
                  : 'text-green-700 bg-green-100 hover:bg-green-200'
              }`}
            >
              <Activity size={16} className="mr-2" />
              {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
            </button>
          )}

          <button
            onClick={() => setShowGoalForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <Plus size={16} className="mr-2" />
            Create Goal
          </button>
        </div>
      </div>

      {/* Metrics Overview */}
      {planningMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            icon={<Target size={24} />}
            title="Total Goals"
            value={planningMetrics.total_goals}
            description="All time goals created"
            color="indigo"
          />
          <MetricCard
            icon={<Activity size={24} />}
            title="Active Goals"
            value={planningMetrics.active_goals}
            description="Currently in progress"
            color="blue"
          />
          <MetricCard
            icon={<CheckCircle size={24} />}
            title="Completed"
            value={planningMetrics.completed_goals}
            description="Successfully finished"
            color="green"
          />
          <MetricCard
            icon={<TrendingUp size={24} />}
            title="Success Rate"
            value={`${planningMetrics.success_rate.toFixed(1)}%`}
            description="Goal completion rate"
            color="purple"
          />
        </div>
      )}

      {/* Goals List */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800">Goals</h3>
        </div>

        {goalsLoading ? (
          <div className="p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading goals...</p>
          </div>
        ) : goals.length === 0 ? (
          <div className="p-6 text-center">
            <Target size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No goals yet</h3>
            <p className="text-gray-600 mb-4">Create your first goal to get started with autonomous planning.</p>
            <button
              onClick={() => setShowGoalForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <Plus size={16} className="mr-2" />
              Create Your First Goal
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {goals.map((goal) => (
              <div key={goal.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-medium text-gray-900 truncate">
                        {goal.description.length > 100 
                          ? `${goal.description.substring(0, 100)}...` 
                          : goal.description
                        }
                      </h4>
                      <StatusBadge status={goal.status} />
                      <PriorityBadge priority={goal.priority} />
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span className="flex items-center">
                        <Clock size={14} className="mr-1" />
                        Created {new Date(goal.created_at).toLocaleDateString()}
                      </span>
                      
                      {goal.deadline && (
                        <span className="flex items-center">
                          <AlertTriangle size={14} className="mr-1" />
                          Due {new Date(goal.deadline).toLocaleDateString()}
                        </span>
                      )}
                      
                      {goal.success_criteria && goal.success_criteria.length > 0 && (
                        <span className="flex items-center">
                          <CheckCircle size={14} className="mr-1" />
                          {goal.success_criteria.length} criteria
                        </span>
                      )}
                      
                      {goal.constraints && goal.constraints.length > 0 && (
                        <span className="flex items-center">
                          <AlertTriangle size={14} className="mr-1" />
                          {goal.constraints.length} constraints
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <GoalActions goal={goal} />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Cost Optimization Insights */}
      <PlanningCostInsights
        goals={goals}
        planningMetrics={planningMetrics}
        className="mt-6"
      />

      {/* Goal Form Modal */}
      <GoalForm
        isOpen={showGoalForm}
        onClose={handleFormClose}
        onSubmit={editingGoal ? handleUpdateGoal : handleCreateGoal}
        initialData={editingGoal}
        isLoading={formLoading}
      />
    </div>
  );
};

export default PlanningDashboard;
