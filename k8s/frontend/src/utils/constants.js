// API endpoints
export const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '';
export const DASHBOARD_API_PREFIX = `${API_BASE_URL}/api`;
export const POLICY_MANAGER_API_PREFIX = `${API_BASE_URL}/api`;
export const PLANNING_API_PREFIX = `${API_BASE_URL}/v1`;

// Ensure consistent endpoint naming
export const POLICIES_ENDPOINT = `${POLICY_MANAGER_API_PREFIX}/policies`;
export const MODEL_PROFILES_ENDPOINT = `${POLICY_MANAGER_API_PREFIX}/model-profiles`; // Use hyphenated version consistently

// Planning API endpoints
export const GOALS_ENDPOINT = `${PLANNING_API_PREFIX}/goals`;
export const TEMPLATES_ENDPOINT = `${PLANNING_API_PREFIX}/templates`;

// Model Profile Constants
export const MOD<PERSON>_PROFILE_CAPABILITIES = [
  'text-generation',
  'chat',
  'code-generation',
  'summarization',
  'complex-reasoning',
  'financial-analysis',
  'customer-support',
  'multi-modal',
  'vision',
  'text-classification',
  'long-context',
  'report-generation',
  'FAQ-answering'
];

export const MODEL_PROFILE_PRICING_TIERS = [
  'free',
  'basic',
  'standard',
  'premium',
  'enterprise',
  'custom'
];

export const MODEL_PROFILE_DATA_SENSITIVITY_LEVELS = [
  'low',
  'medium',
  'high'
];

export const MODEL_PROFILE_BACKEND_TYPES = [
  'openai',
  'openai-external',
  'anthropic',
  'anthropic-external',
  'google',
  'google-external',
  'vllm',
  'llama',
  'mistral',
  'custom'
];

// Planning Constants
export const GOAL_STATUSES = [
  'pending',
  'planning',
  'ready',
  'executing',
  'completed',
  'failed',
  'cancelled'
];

export const TASK_STATUSES = [
  'pending',
  'ready',
  'running',
  'completed',
  'failed',
  'skipped'
];

export const CONSTRAINT_TYPES = [
  'time',
  'cost',
  'quality',
  'resource'
];

export const CONSTRAINT_OPERATORS = [
  '<=',
  '>=',
  '==',
  '!=',
  '<',
  '>'
];

export const CONSTRAINT_SEVERITIES = [
  'soft',
  'hard',
  'preference'
];

export const SUCCESS_CRITERIA_OPERATORS = [
  '>=',
  '<=',
  '==',
  '!=',
  '>',
  '<',
  'contains',
  'not_contains'
];

export const GOAL_PRIORITIES = [
  { value: 1, label: 'Low (1)' },
  { value: 2, label: 'Low (2)' },
  { value: 3, label: 'Low (3)' },
  { value: 4, label: 'Medium (4)' },
  { value: 5, label: 'Medium (5)' },
  { value: 6, label: 'Medium (6)' },
  { value: 7, label: 'High (7)' },
  { value: 8, label: 'High (8)' },
  { value: 9, label: 'Critical (9)' },
  { value: 10, label: 'Critical (10)' }
];
