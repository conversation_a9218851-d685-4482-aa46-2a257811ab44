# AI Cost-Performance Optimizer: Developer Guide

This guide provides developers with detailed instructions for integrating applications with the AI Cost-Performance Optimizer, featuring comprehensive Multi-Agent Orchestration and Collaboration capabilities.

## Integration Overview

The AI Cost-Performance Optimizer provides three main integration approaches:

1. **Direct LLM Integration**: Send individual LLM requests through our intelligent Proxy Gateway for cost optimization and routing
2. **Autonomous Planning Integration**: Submit high-level goals to our Planning Engine for autonomous multi-step task execution
3. **Multi-Agent Orchestration**: Leverage our comprehensive multi-agent platform for complex collaborative AI workflows

All approaches leverage the same underlying infrastructure for cost optimization, policy enforcement, performance monitoring, and security.

## Getting Started

### Core Endpoints

The system provides multiple integration points depending on your use case:

**Direct LLM API Endpoint:**
```
POST https://scale-llm.com/v1/chat/completions
```

**Autonomous Planning API Endpoints:**
```
POST https://scale-llm.com/v1/goals              # Create goal
POST https://scale-llm.com/v1/goals/{id}/plan    # Generate plan
POST https://scale-llm.com/v1/goals/{id}/execute # Execute plan
GET  https://scale-llm.com/v1/goals/{id}/status  # Monitor progress
GET  https://scale-llm.com/v1/goals/{id}/tasks   # Get task details
GET  https://scale-llm.com/v1/goals/{id}/results # Get execution results
```

**Multi-Agent Orchestration API Endpoints:**
```
# Authentication
POST /auth/login                                 # User authentication
POST /auth/logout                                # User logout

# Agent Management
GET    /v1/agents                                # List agents
POST   /v1/agents                                # Register agent
GET    /v1/agents/{id}                           # Get agent details
PUT    /v1/agents/{id}                           # Update agent
DELETE /v1/agents/{id}                           # Unregister agent
POST   /v1/agents/{id}/health                    # Health check
GET    /v1/agents/{id}/insights                  # Performance insights
POST   /v1/agents/select                         # Optimal agent selection

# Workflow Management
GET    /v1/workflows                             # List workflows
POST   /v1/workflows                             # Create workflow
GET    /v1/workflows/{id}                        # Get workflow details
PUT    /v1/workflows/{id}                        # Update workflow
DELETE /v1/workflows/{id}                        # Delete workflow
POST   /v1/workflows/{id}/execute                # Execute workflow
POST   /v1/workflows/{id}/pause                  # Pause workflow
POST   /v1/workflows/{id}/resume                 # Resume workflow
POST   /v1/workflows/{id}/cancel                 # Cancel workflow
GET    /v1/workflows/{id}/status                 # Get execution status

# Analytics
GET    /v1/analytics/agents                      # Agent analytics
GET    /v1/analytics/workflows                   # Workflow analytics
GET    /v1/analytics/collaboration               # Collaboration metrics
```

**Dashboard UI:**
```
https://scale-llm.com/                           # Main dashboard
https://scale-llm.com/#planning                  # Planning tab (direct access)
https://scale-llm.com/#multi-agent               # Multi-Agent tab (direct access)
```

## Integration Approaches

### Approach 1: Multi-Agent Dashboard UI Integration

For users who prefer a comprehensive graphical interface, access the Multi-Agent Dashboard through the web UI:

**Features Available:**
- **Agent Registry**: Manage and monitor AI agents with health checks and performance insights
- **Agent Marketplace**: Discover and integrate external agents from multiple providers
- **Workflow Designer**: Create complex multi-agent workflows with visual design tools
- **Real-time Visualization**: Interactive workflow graphs with live execution monitoring
- **Template Library**: Access pre-built workflow templates for common use cases
- **Analytics Dashboard**: Comprehensive performance and collaboration analytics
- **Security Management**: Role-based access control with audit logging

**Getting Started:**
1. Navigate to the main dashboard at `https://scale-llm.com/`
2. Click on the "Multi-Agent" tab in the navigation
3. Register your first agent or browse the marketplace
4. Create workflows using templates or custom designs
5. Monitor execution with real-time visualization

### Approach 2: Planning Dashboard UI Integration

For autonomous goal-based workflows, access the Planning Dashboard:

**Features Available:**
- **Goal Creation**: User-friendly form with guided input for success criteria and constraints
- **Real-time Monitoring**: Live progress tracking with automatic status updates
- **Task Visualization**: Interactive dependency graphs showing execution flow
- **Cost Optimization**: AI-powered insights and recommendations for cost efficiency
- **Progress Analytics**: Detailed metrics on goal success rates and performance

**Getting Started:**
1. Navigate to the main dashboard at `https://scale-llm.com/`
2. Click on the "Planning" tab in the navigation
3. Click "Create Goal" to define your first objective
4. Fill in the goal description, success criteria, and constraints
5. Generate a plan and execute it with real-time monitoring

### Approach 3: Direct LLM Integration

For traditional LLM usage, send requests to the Proxy Gateway using the standard Chat Completion API format:

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {"role": "user", "content": "Your prompt here"}
  ],
  "temperature": 0.7,
  "max_tokens": 150
}
```

### Approach 4: Multi-Agent API Integration

For programmatic multi-agent orchestration, use the comprehensive API:

```json
{
  "name": "Customer Analysis Workflow",
  "description": "Analyze customer feedback and generate insights",
  "agents": [
    {
      "role": "data_collector",
      "agent_id": "agent-123",
      "capabilities": ["data_collection", "web_scraping"]
    },
    {
      "role": "analyst",
      "agent_id": "agent-456",
      "capabilities": ["statistical_analysis", "sentiment_analysis"]
    }
  ],
  "tasks": [
    {
      "name": "Collect Feedback",
      "type": "data_collection",
      "assigned_role": "data_collector",
      "parameters": {
        "source": "customer_feedback_db",
        "time_range": "last_30_days"
      }
    },
    {
      "name": "Analyze Sentiment",
      "type": "analysis",
      "assigned_role": "analyst",
      "dependencies": ["Collect Feedback"]
    }
  ]
}
```

### Approach 5: Autonomous Goal-Based Integration

For complex, multi-step workflows, submit high-level goals to the Planning Engine:

```json
{
  "description": "Analyze customer feedback from the last month and generate actionable insights",
  "success_criteria": [
    {
      "description": "Process at least 100 feedback entries",
      "metric": "feedback_count",
      "target": 100,
      "operator": ">=",
      "weight": 0.8,
      "required": true
    }
  ],
  "constraints": [
    {
      "type": "time",
      "description": "Complete within 2 hours",
      "limit": "2h",
      "operator": "<=",
      "severity": "hard"
    }
  ],
  "priority": 8
}
```

## Key Features

### Multi-Agent Orchestration

The platform provides comprehensive multi-agent collaboration capabilities:

#### Agent Registry and Management
- **Agent Registration**: Register specialized AI agents with capabilities, performance metrics, and metadata
- **Health Monitoring**: Continuous health checks with automatic status updates
- **Performance Tracking**: Real-time metrics on agent utilization, success rates, and response times
- **Lifecycle Management**: Complete agent lifecycle from registration to decommissioning

#### Intelligent Agent Selection
- **ML-driven Selection**: Advanced algorithms using performance history and capability matching
- **Workload Balancing**: Automatic load distribution across available agents
- **Cost Optimization**: Budget-aware agent selection with cost constraints
- **Confidence Scoring**: Quality assessment based on historical performance data

#### Workflow Orchestration
- **Visual Workflow Designer**: Drag-and-drop interface for creating complex multi-agent workflows
- **Template Library**: Pre-built workflow templates for common use cases
- **Dependency Management**: Automatic handling of task dependencies and execution order
- **Real-time Monitoring**: Live visualization of workflow execution with status updates

#### Agent Marketplace
- **Multi-provider Support**: Integration with HuggingFace, OpenAI, Anthropic, and custom providers
- **Agent Evaluation**: Automated benchmarking and security scanning
- **Community Ratings**: User reviews and popularity metrics
- **One-click Integration**: Seamless agent integration with configuration management

#### Advanced Security
- **Role-based Access Control**: Granular permissions for agents, workflows, and resources
- **Authentication**: JWT-based session management with secure token handling
- **Audit Logging**: Comprehensive security event tracking and compliance reporting
- **Encrypted Communication**: Secure channels for inter-agent communication

### Intelligent LLM Routing

Based on configured policies, the proxy automatically selects the most appropriate backend LLM for each request. Routing decisions can be based on:
- Prompt content and complexity
- User roles and permissions
- Token length and cost constraints
- Performance/latency requirements

### Autonomous Task Decomposition

The Planning Engine automatically breaks down complex goals into executable tasks:
- **Natural Language Processing**: Parse goals from plain English descriptions
- **Intelligent Planning**: Use LLM-assisted decomposition with task templates
- **Dependency Management**: Handle complex task relationships and execution order
- **Resource Optimization**: Estimate and manage computational resources

### Cost & Performance Optimization

Unified optimization across both individual requests and multi-step workflows:
- `OPTIMIZE_LATENCY`: Prioritize response speed
- `OPTIMIZE_COST`: Prioritize lower cost
- `OPTIMIZE_BALANCED`: Balance performance and cost
- **Workflow-level optimization**: Optimize entire goal execution for cost and time

### Execution Orchestration

For goal-based workflows, the system provides:
- **Parallel Execution**: Run independent tasks concurrently
- **Failure Handling**: Automatic retry with exponential backoff
- **Progress Tracking**: Real-time monitoring of task completion
- **State Management**: Persistent context across multi-step executions

### Conversational Context Management

For multi-turn conversations, the proxy maintains context automatically:
1. For the first turn, omit `conversation_id` - the proxy will generate one
2. Include this `conversation_id` in subsequent requests
3. The proxy automatically retrieves prior messages before sending to the chosen LLM

### Policy Application

Benefit from centrally defined policies without implementing complex routing logic in your application. Policies can be configured through the admin dashboard and apply to both direct LLM calls and autonomous workflows.

## API Usage Examples

### Direct LLM API Examples

#### Basic Chat Completion

The proxy will automatically select the best LLM based on configured policies:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Tell me a short, imaginative story about a space squirrel."}
    ],
    "temperature": 0.7,
    "max_tokens": 150
  }'
```

### Conversational Turn 1 (New Conversation)

For the first turn, omit `conversation_id`:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Hi, who are you and what do you do?"}
    ]
  }'
```

The response will contain a `conversation_id` in the payload.

### Conversational Turn 2 (Continuing the Conversation)

Use the `conversation_id` from the first turn:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "conversation_id": "<YOUR_CONVERSATION_ID_FROM_PREVIOUS_RESPONSE>",
    "model": "claude-3-opus-********",
    "messages": [
      {"role": "user", "content": "Can you elaborate on the second part of your previous answer?"}
    ]
  }'
```

### Manual Model Override

Override automatic routing by specifying a preferred LLM ID:

```bash
curl -X POST \
  https://scale-llm.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "X-Preferred-LLM-ID: gpt-4-turbo-us-east" \
  -d '{
    "model": "gpt-3.5-turbo",
    "messages": [
      {"role": "user", "content": "Explain quantum entanglement in simple terms."}
    ]
  }'
```

**Note:** The `model` field in the request body still acts as a hint or fallback if no policy matches or if the preferred LLM is unavailable.

### Multi-Agent Orchestration API Examples

#### Agent Registration and Management

**Register a New Agent:**
```bash
curl -X POST \
  https://scale-llm.com/v1/agents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Data Analysis Expert",
    "type": "data_analyst",
    "description": "Specialized agent for statistical analysis and data visualization",
    "endpoint": "https://my-agent.com/api",
    "capabilities": [
      {
        "id": "statistical_analysis",
        "name": "Statistical Analysis",
        "description": "Advanced statistical analysis capabilities",
        "quality": 0.95,
        "cost": 0.10,
        "speed": 0.85
      },
      {
        "id": "data_visualization",
        "name": "Data Visualization",
        "description": "Create charts and graphs",
        "quality": 0.90,
        "cost": 0.08,
        "speed": 0.90
      }
    ],
    "metadata": {
      "version": "1.0",
      "provider": "custom",
      "max_concurrent_tasks": 5
    }
  }'
```

**Create Multi-Agent Workflow:**
```bash
curl -X POST \
  https://scale-llm.com/v1/workflows \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Customer Feedback Analysis Pipeline",
    "description": "Comprehensive analysis of customer feedback with insights generation",
    "tasks": [
      {
        "name": "Collect Feedback Data",
        "type": "data_collection",
        "description": "Gather customer feedback from multiple sources",
        "agent_requirements": {
          "type": "data_analyst",
          "capabilities": ["data_collection"]
        },
        "parameters": {
          "sources": ["support_tickets", "surveys", "reviews"],
          "time_range": "last_30_days"
        },
        "priority": 1
      },
      {
        "name": "Sentiment Analysis",
        "type": "analysis",
        "description": "Analyze sentiment of collected feedback",
        "agent_requirements": {
          "type": "data_analyst",
          "capabilities": ["sentiment_analysis"]
        },
        "dependencies": ["Collect Feedback Data"],
        "priority": 2
      },
      {
        "name": "Generate Insights Report",
        "type": "report_generation",
        "description": "Create comprehensive insights report",
        "agent_requirements": {
          "type": "content_writer",
          "capabilities": ["report_generation"]
        },
        "dependencies": ["Sentiment Analysis"],
        "priority": 3
      }
    ],
    "execution_config": {
      "max_concurrency": 3,
      "timeout": "1h",
      "retry_policy": {
        "max_retries": 3,
        "backoff_strategy": "exponential"
      }
    }
  }'
```

**Execute Workflow:**
```bash
curl -X POST \
  https://scale-llm.com/v1/workflows/{workflow_id}/execute \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Monitor Workflow Status:**
```bash
curl https://scale-llm.com/v1/workflows/{workflow_id}/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Agent Selection and Optimization

**Get Optimal Agent for Task:**
```bash
curl -X POST \
  https://scale-llm.com/v1/agents/select \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "task_requirements": {
      "type": "data_analysis",
      "capabilities": ["statistical_analysis", "data_visualization"],
      "complexity": "high",
      "estimated_tokens": 5000
    },
    "constraints": {
      "max_cost": 2.00,
      "max_latency": 30000,
      "quality_threshold": 0.85
    },
    "optimization_strategy": "balanced"
  }'
```

### Autonomous Planning API Examples

#### Create and Execute a Goal

**Step 1: Create a Goal**
```bash
curl -X POST \
  https://scale-llm.com/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "description": "Analyze customer support tickets from the last week and generate a comprehensive report with recommendations",
    "success_criteria": [
      {
        "description": "Process at least 100 tickets",
        "metric": "ticket_count",
        "target": 100,
        "operator": ">=",
        "weight": 0.8,
        "required": true
      }
    ],
    "constraints": [
      {
        "type": "time",
        "description": "Complete within 2 hours",
        "limit": "2h",
        "operator": "<=",
        "severity": "hard"
      }
    ],
    "priority": 8
  }'
```

**Step 2: Generate Execution Plan**
```bash
# Use the goal_id from step 1
curl -X POST \
  https://scale-llm.com/v1/goals/{goal_id}/plan \
  -H "X-User-ID: your-user-id"
```

**Step 3: Execute the Plan**
```bash
curl -X POST \
  https://scale-llm.com/v1/goals/{goal_id}/execute \
  -H "Content-Type: application/json" \
  -H "X-User-ID: your-user-id" \
  -d '{
    "max_concurrency": 3,
    "timeout": "30m"
  }'
```

**Step 4: Monitor Progress**
```bash
# Check execution status
curl https://scale-llm.com/v1/goals/{goal_id}/status \
  -H "X-User-ID: your-user-id"

# Get detailed task progress
curl https://scale-llm.com/v1/goals/{goal_id}/tasks \
  -H "X-User-ID: your-user-id"

# Get final results
curl https://scale-llm.com/v1/goals/{goal_id}/results \
  -H "X-User-ID: your-user-id"
```

#### Common Goal Types

**Data Analysis Workflow:**
```json
{
  "description": "Perform comprehensive sales analysis for Q4 and predict Q1 performance",
  "success_criteria": [
    {
      "description": "Analyze at least 6 months of data",
      "metric": "data_coverage_months",
      "target": 6,
      "operator": ">=",
      "weight": 0.9,
      "required": true
    }
  ],
  "context": {
    "data_sources": ["sales_db", "customer_db"],
    "analysis_period": "Q4_2023",
    "prediction_horizon": "Q1_2024"
  }
}
```

**Content Creation Workflow:**
```json
{
  "description": "Create a complete marketing campaign for our new product launch",
  "success_criteria": [
    {
      "description": "Generate 5 blog posts",
      "metric": "blog_posts_count",
      "target": 5,
      "operator": ">=",
      "weight": 0.8,
      "required": true
    },
    {
      "description": "Create 20 social media posts",
      "metric": "social_posts_count",
      "target": 20,
      "operator": ">=",
      "weight": 0.7,
      "required": true
    }
  ],
  "context": {
    "product_name": "AI Assistant Pro",
    "target_audience": "business professionals",
    "launch_date": "2024-03-15"
  }
}
```

## Planning Dashboard User Interface

The AI Cost-Performance Optimizer now includes a comprehensive Planning Dashboard that provides a user-friendly interface for creating, monitoring, and optimizing autonomous planning workflows.

### Dashboard Features

#### Goal Creation Interface
- **Intuitive Form**: User-friendly goal creation with guided input fields
- **Success Criteria**: Define measurable success metrics with targets and weights
- **Constraints**: Set time, cost, quality, and resource constraints with severity levels
- **Priority Management**: Set goal priorities from 1-10 with visual indicators
- **Validation**: Real-time form validation with helpful error messages

#### Real-time Monitoring
- **Live Progress Tracking**: Automatic status updates every 5 seconds for active goals
- **Visual Indicators**: Color-coded status badges and progress bars
- **Notification System**: Automatic alerts for status changes and task completions
- **Monitoring Controls**: Start/stop monitoring with visual feedback

#### Task Dependency Visualization
- **Interactive Graphs**: SVG-based dependency visualization with zoom and scroll
- **Topological Layout**: Automatic arrangement showing execution flow
- **Status Indicators**: Color-coded tasks showing current execution state
- **Responsive Design**: Adapts to different screen sizes and task counts

#### Cost Optimization Insights
- **Cost Analysis**: Real-time cost tracking with budget vs. actual comparisons
- **Efficiency Metrics**: Cost efficiency percentages and trend analysis
- **AI Recommendations**: Intelligent optimization suggestions based on execution patterns
- **Priority Breakdown**: Cost analysis by goal priority levels
- **Quick Actions**: One-click optimization tools and analysis shortcuts

### Accessing the Dashboard

1. **Web Interface**: Navigate to `https://scale-llm.com/` and click the "Planning" tab
2. **Direct Access**: Use `https://scale-llm.com/#planning` for direct planning dashboard access
3. **Mobile Support**: Responsive design works on tablets and mobile devices

### Dashboard Workflow

1. **Create Goals**: Click "Create Goal" and fill in the comprehensive form
2. **Generate Plans**: Click "Generate Plan" to decompose goals into executable tasks
3. **Monitor Execution**: Use real-time monitoring to track progress and status
4. **Analyze Performance**: Review cost insights and optimization recommendations
5. **Optimize**: Apply suggested optimizations for better cost efficiency

### Technical Implementation

The Planning Dashboard is built with modern web technologies and integrates seamlessly with the existing AI Optimizer infrastructure:

#### Frontend Architecture
- **Framework**: React 18 with modern hooks and functional components
- **Styling**: Tailwind CSS for consistent, responsive design
- **Icons**: Lucide React for modern, scalable iconography
- **State Management**: Custom hooks with local state management
- **Real-time Updates**: Polling-based monitoring system (5-second intervals)

#### Key Components
- **PlanningDashboard.jsx**: Main dashboard interface with goal management
- **GoalDetailView.jsx**: Detailed goal monitoring with real-time updates
- **TaskDependencyGraph.jsx**: Interactive SVG-based dependency visualization
- **PlanningCostInsights.jsx**: Cost analysis with AI-powered recommendations
- **GoalForm.jsx**: Comprehensive goal creation form with validation

#### API Integration
- **Planning Management Hook**: Complete CRUD operations for goals, plans, and tasks
- **Real-time Monitoring Hook**: Automatic polling and notification system
- **Type Safety**: Comprehensive type definitions for all planning entities
- **Error Handling**: Robust error handling with user-friendly messages

#### Performance Features
- **Lazy Loading**: Components load only when needed
- **Optimized Rendering**: Efficient re-rendering with React optimization patterns
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Caching**: Smart caching of API responses to reduce server load

## Client Libraries

For improved developer experience, we provide client libraries that abstract the HTTP calls and handle direct LLM integration, autonomous planning workflows, and multi-agent orchestration.

### Python Client Library

#### Direct LLM Client

```python
import requests
import json
from typing import List, Dict, Any, Optional

class LLMOptimizerClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.chat_completions_endpoint = f"{self.base_url}/v1/chat/completions"

    def create_chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        conversation_id: Optional[str] = None,
        preferred_llm_id: Optional[str] = None,
        **kwargs: Any
    ) -> Dict[str, Any]:
        """
        Sends a chat completion request to the LLM Optimizer Proxy.

        Args:
            messages (List[Dict[str, str]]): List of message objects.
            model (str): The model name (used as a hint/fallback by the proxy).
            temperature (float): Sampling temperature.
            max_tokens (Optional[int]): Maximum number of tokens to generate.
            conversation_id (Optional[str]): ID to continue a conversation.
            preferred_llm_id (Optional[str]): Forces routing to a specific LLM.
            **kwargs: Additional parameters to pass to the LLM API.

        Returns:
            Dict[str, Any]: The response payload from the LLM.
        """
        payload = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            **kwargs
        }
        if max_tokens is not None:
            payload["max_tokens"] = max_tokens
        if conversation_id:
            payload["conversation_id"] = conversation_id

        headers = {"Content-Type": "application/json"}
        if preferred_llm_id:
            headers["X-Preferred-LLM-ID"] = preferred_llm_id

        try:
            response = requests.post(
                self.chat_completions_endpoint,
                headers=headers,
                json=payload
            )
            response.raise_for_status()  # Raise an exception for HTTP errors
            return response.json()
        except requests.exceptions.HTTPError as http_err:
            print(f"HTTP error occurred: {http_err} - {response.text}")
            raise
        except requests.exceptions.ConnectionError as conn_err:
            print(f"Connection error occurred: {conn_err}")
            raise
        except Exception as err:
            print(f"An unexpected error occurred: {err}")
            raise

#### Multi-Agent Orchestration Client

```python
class MultiAgentClient:
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url
        self.auth_token = auth_token
        self.agents_endpoint = f"{self.base_url}/v1/agents"
        self.workflows_endpoint = f"{self.base_url}/v1/workflows"

    def _get_headers(self):
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.auth_token}"
        }

    def register_agent(
        self,
        name: str,
        agent_type: str,
        description: str,
        endpoint: str,
        capabilities: List[Dict],
        metadata: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Register a new agent in the system."""
        payload = {
            "name": name,
            "type": agent_type,
            "description": description,
            "endpoint": endpoint,
            "capabilities": capabilities
        }
        if metadata:
            payload["metadata"] = metadata

        response = requests.post(
            self.agents_endpoint,
            headers=self._get_headers(),
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def create_workflow(
        self,
        name: str,
        description: str,
        tasks: List[Dict],
        execution_config: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Create a new multi-agent workflow."""
        payload = {
            "name": name,
            "description": description,
            "tasks": tasks
        }
        if execution_config:
            payload["execution_config"] = execution_config

        response = requests.post(
            self.workflows_endpoint,
            headers=self._get_headers(),
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def execute_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Execute a workflow."""
        response = requests.post(
            f"{self.workflows_endpoint}/{workflow_id}/execute",
            headers=self._get_headers()
        )
        response.raise_for_status()
        return response.json()

    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow execution status."""
        response = requests.get(
            f"{self.workflows_endpoint}/{workflow_id}/status",
            headers=self._get_headers()
        )
        response.raise_for_status()
        return response.json()

    def select_optimal_agent(
        self,
        task_requirements: Dict,
        constraints: Optional[Dict] = None,
        optimization_strategy: str = "balanced"
    ) -> Dict[str, Any]:
        """Select optimal agent for a task."""
        payload = {
            "task_requirements": task_requirements,
            "optimization_strategy": optimization_strategy
        }
        if constraints:
            payload["constraints"] = constraints

        response = requests.post(
            f"{self.agents_endpoint}/select",
            headers=self._get_headers(),
            json=payload
        )
        response.raise_for_status()
        return response.json()
```

#### Autonomous Planning Client

```python
class PlanningClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.goals_endpoint = f"{self.base_url}/v1/goals"

    def create_goal(
        self,
        description: str,
        user_id: str,
        success_criteria: Optional[List[Dict]] = None,
        constraints: Optional[List[Dict]] = None,
        context: Optional[Dict] = None,
        priority: int = 5
    ) -> Dict[str, Any]:
        """Create a new goal for autonomous execution."""
        payload = {
            "description": description,
            "priority": priority
        }
        if success_criteria:
            payload["success_criteria"] = success_criteria
        if constraints:
            payload["constraints"] = constraints
        if context:
            payload["context"] = context

        headers = {
            "Content-Type": "application/json",
            "X-User-ID": user_id
        }

        response = requests.post(self.goals_endpoint, headers=headers, json=payload)
        response.raise_for_status()
        return response.json()

    def create_plan(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Generate an execution plan for a goal."""
        headers = {"X-User-ID": user_id}
        response = requests.post(f"{self.goals_endpoint}/{goal_id}/plan", headers=headers)
        response.raise_for_status()
        return response.json()

    def execute_goal(
        self,
        goal_id: str,
        user_id: str,
        max_concurrency: int = 3,
        timeout: str = "30m"
    ) -> Dict[str, Any]:
        """Execute a goal's plan."""
        payload = {
            "max_concurrency": max_concurrency,
            "timeout": timeout
        }
        headers = {
            "Content-Type": "application/json",
            "X-User-ID": user_id
        }
        response = requests.post(
            f"{self.goals_endpoint}/{goal_id}/execute",
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        return response.json()

    def get_status(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Get goal execution status."""
        headers = {"X-User-ID": user_id}
        response = requests.get(f"{self.goals_endpoint}/{goal_id}/status", headers=headers)
        response.raise_for_status()
        return response.json()

    def get_results(self, goal_id: str, user_id: str) -> Dict[str, Any]:
        """Get goal execution results."""
        headers = {"X-User-ID": user_id}
        response = requests.get(f"{self.goals_endpoint}/{goal_id}/results", headers=headers)
        response.raise_for_status()
        return response.json()
```

### Usage Examples

#### Multi-Agent Orchestration Usage

```python
# Initialize the multi-agent client
multi_agent_client = MultiAgentClient(OPTIMIZER_BASE_URL, "your-auth-token")

# Example 1: Register a custom agent
try:
    agent = multi_agent_client.register_agent(
        name="Advanced Data Analyst",
        agent_type="data_analyst",
        description="Specialized in complex statistical analysis and ML modeling",
        endpoint="https://my-agent-service.com/api",
        capabilities=[
            {
                "id": "statistical_analysis",
                "name": "Statistical Analysis",
                "description": "Advanced statistical analysis capabilities",
                "quality": 0.95,
                "cost": 0.12,
                "speed": 0.88
            },
            {
                "id": "ml_modeling",
                "name": "Machine Learning Modeling",
                "description": "Build and train ML models",
                "quality": 0.92,
                "cost": 0.25,
                "speed": 0.75
            }
        ],
        metadata={
            "version": "2.1",
            "provider": "custom",
            "max_concurrent_tasks": 3,
            "specializations": ["time_series", "classification", "regression"]
        }
    )
    print(f"Registered agent: {agent['id']}")

    # Example 2: Create and execute a multi-agent workflow
    workflow = multi_agent_client.create_workflow(
        name="Customer Churn Analysis Pipeline",
        description="Comprehensive customer churn analysis with predictive modeling",
        tasks=[
            {
                "name": "Data Collection",
                "type": "data_collection",
                "description": "Collect customer data from multiple sources",
                "agent_requirements": {
                    "type": "data_analyst",
                    "capabilities": ["data_collection", "data_integration"]
                },
                "parameters": {
                    "sources": ["crm", "billing", "support_tickets"],
                    "time_range": "last_12_months"
                },
                "priority": 1
            },
            {
                "name": "Feature Engineering",
                "type": "data_processing",
                "description": "Create features for churn prediction",
                "agent_requirements": {
                    "type": "data_analyst",
                    "capabilities": ["feature_engineering", "data_preprocessing"]
                },
                "dependencies": ["Data Collection"],
                "priority": 2
            },
            {
                "name": "Churn Prediction Model",
                "type": "ml_modeling",
                "description": "Build and train churn prediction model",
                "agent_requirements": {
                    "type": "data_analyst",
                    "capabilities": ["ml_modeling", "model_evaluation"]
                },
                "dependencies": ["Feature Engineering"],
                "priority": 3
            },
            {
                "name": "Generate Insights Report",
                "type": "report_generation",
                "description": "Create comprehensive analysis report",
                "agent_requirements": {
                    "type": "content_writer",
                    "capabilities": ["report_generation", "data_visualization"]
                },
                "dependencies": ["Churn Prediction Model"],
                "priority": 4
            }
        ],
        execution_config={
            "max_concurrency": 2,
            "timeout": "2h",
            "retry_policy": {
                "max_retries": 3,
                "backoff_strategy": "exponential"
            }
        }
    )

    workflow_id = workflow["id"]
    print(f"Created workflow: {workflow_id}")

    # Execute the workflow
    execution = multi_agent_client.execute_workflow(workflow_id)
    print("Workflow execution started")

    # Monitor progress
    import time
    while True:
        status = multi_agent_client.get_workflow_status(workflow_id)
        print(f"Workflow status: {status['status']}")

        if status["status"] in ["completed", "failed", "cancelled"]:
            break
        time.sleep(10)

    print("Workflow completed!")

    # Example 3: Intelligent agent selection
    optimal_agent = multi_agent_client.select_optimal_agent(
        task_requirements={
            "type": "data_analysis",
            "capabilities": ["statistical_analysis", "data_visualization"],
            "complexity": "high",
            "estimated_tokens": 8000
        },
        constraints={
            "max_cost": 3.00,
            "max_latency": 45000,
            "quality_threshold": 0.90
        },
        optimization_strategy="quality_first"
    )

    print(f"Selected agent: {optimal_agent['agent']['name']}")
    print(f"Confidence score: {optimal_agent['confidence_score']}")
    print(f"Estimated cost: ${optimal_agent['estimated_cost']:.2f}")

except Exception as e:
    print(f"Error in multi-agent workflow: {e}")
```

#### Direct LLM Usage

```python
# Replace with your actual deployed frontend IP
OPTIMIZER_BASE_URL = "https://scale-llm.com"  # Or your actual IP/hostname

llm_client = LLMOptimizerClient(OPTIMIZER_BASE_URL)

# Example 1: Basic Request (Proxy chooses LLM)
try:
    response_basic = llm_client.create_chat_completion(
        messages=[{"role": "user", "content": "What is the capital of France?"}],
        model="gpt-3.5-turbo"  # Hint model
    )
    print("Basic Request Response:", json.dumps(response_basic, indent=2))
except Exception as e:
    print(f"Error during basic request: {e}")
```

#### Autonomous Planning Usage

```python
planning_client = PlanningClient(OPTIMIZER_BASE_URL)

# Example: Customer Service Analysis Workflow
try:
    # Step 1: Create goal
    goal = planning_client.create_goal(
        description="Analyze customer support tickets from last week and generate insights",
        user_id="analyst-team",
        success_criteria=[
            {
                "description": "Process at least 100 tickets",
                "metric": "ticket_count",
                "target": 100,
                "operator": ">=",
                "weight": 0.8,
                "required": True
            }
        ],
        constraints=[
            {
                "type": "time",
                "description": "Complete within 2 hours",
                "limit": "2h",
                "operator": "<=",
                "severity": "hard"
            }
        ],
        context={
            "data_source": "support_tickets_db",
            "time_range": "last_7_days"
        },
        priority=8
    )

    goal_id = goal["id"]
    print(f"Created goal: {goal_id}")

    # Step 2: Generate plan
    plan = planning_client.create_plan(goal_id, "analyst-team")
    print(f"Generated plan with {len(plan['tasks'])} tasks")

    # Step 3: Execute
    execution = planning_client.execute_goal(goal_id, "analyst-team", max_concurrency=3)
    print("Execution started")

    # Step 4: Monitor progress
    import time
    while True:
        status = planning_client.get_status(goal_id, "analyst-team")
        print(f"Status: {status['status']}")

        if status["status"] in ["completed", "failed"]:
            break
        time.sleep(10)

    # Step 5: Get results
    if status["status"] == "completed":
        results = planning_client.get_results(goal_id, "analyst-team")
        print("Results:", json.dumps(results, indent=2))

except Exception as e:
    print(f"Error during autonomous workflow: {e}")
```

### Other Available Client Libraries

- **JavaScript/TypeScript**: For Node.js backends or browser-based applications
- **Go**: Native integration for Go services
- **Ruby, Java, C#**: Available based on your development ecosystem

## Benefits of Integration

### "Set it and Forget it" Optimization
Write code once to hit our proxy, and the backend infrastructure automatically handles cost and performance decisions for individual requests, complex workflows, and multi-agent orchestration.

### Multi-Agent Collaboration
Leverage our comprehensive multi-agent platform to:
- **Orchestrate Complex Workflows**: Coordinate multiple specialized agents working together
- **Intelligent Agent Selection**: Automatically choose optimal agents based on capabilities and performance
- **Real-time Monitoring**: Track agent performance and workflow execution in real-time
- **Marketplace Integration**: Access a growing ecosystem of specialized AI agents
- **Template-based Workflows**: Use pre-built patterns for common multi-agent scenarios

### Autonomous Workflow Execution
Submit high-level goals and let the system automatically:
- Break down complex objectives into executable tasks
- Orchestrate multi-step workflows with proper dependencies
- Handle failures and retries intelligently
- Optimize costs across the entire workflow

### Enterprise-Grade Security
Benefit from comprehensive security features:
- **Role-based Access Control**: Granular permissions for agents, workflows, and resources
- **Audit Logging**: Complete security event tracking and compliance reporting
- **Secure Communication**: Encrypted channels for inter-agent communication
- **Authentication**: JWT-based session management with secure token handling

### Future-Proofing
As new LLMs emerge, agents become available, or orchestration capabilities improve, we can update backend policies and algorithms without requiring any code changes in your applications.

### Simplified AI Integration
- No need to manage API keys for multiple LLM providers or agent services
- No complex workflow orchestration logic in your application
- No manual task decomposition, dependency management, or agent coordination
- Unified interface for simple requests, complex workflows, and multi-agent collaboration

### Centralized Observability
Use our comprehensive dashboard to monitor:
- Individual LLM request performance and costs
- Goal execution progress and success rates
- Agent performance metrics and health status
- Workflow execution with real-time visualization
- Multi-agent collaboration analytics and insights
- Security events and compliance reporting

### Reduced Development Time
- Less boilerplate code for LLM and agent integration
- No need to build custom workflow orchestration or agent management
- Automatic handling of complex multi-step AI processes and agent coordination
- Built-in retry logic, error handling, and failure recovery
- Pre-built templates for common multi-agent scenarios

## Next Steps

- Review the [Setup Guide](setup_guide.md) for installation details
- Explore [Routing Examples](routing_examples.md) for policy configurations
- Learn about the [Cost Model](cost_model.md) for optimization strategies
- Understand the [System Architecture](architecture.md) for integration planning
- Check out [Planning Engine Examples](planning_engine_examples.md) for autonomous workflow patterns
- Read the [Planning Engine Integration Guide](planning_engine_integration.md) for advanced setup

## Prompt Management API

The Policy Manager provides API endpoints for managing LLM prompts.

**Create a Prompt**
```
POST /prompts
```
**Get All Prompts**
```
GET /prompts
```
**Get a Specific Prompt**
```
GET /prompts/{id}
```
**Update a Prompt**
```
PUT /prompts/{id}
```
**Delete a Prompt**
```
DELETE /prompts/{id}
```

For more details, refer to the Policy Manager documentation.

## Synthetic Data Generation

The data processor includes a synthetic data generation loop that periodically generates synthetic LLM inference data. This data is used for testing and evaluation purposes.

The synthetic data generation loop is configured using the following environment variables:

*   `SYNTHETIC_DATA_GENERATION_MODEL_ID`: Model ID for synthetic data generation.
*   `FEEDBACK_LOOP_INTERVAL`: Interval between synthetic data generation runs.

## Evaluation Results API

The Dashboard API provides endpoints for retrieving evaluation results and curated data.

**Get Evaluation Results**
```
GET /evaluation-results
```
**Get Curated Data**
```
GET /curated-data
```

For more details, refer to the Dashboard API documentation.

## Planning Dashboard Troubleshooting

### Common Issues and Solutions

#### Dashboard Not Loading
**Problem**: Planning tab shows loading spinner indefinitely
**Solutions**:
1. Check that the planning service is running: `kubectl get pods | grep planning-service`
2. Verify API endpoints are accessible: `curl https://scale-llm.com/v1/goals`
3. Check browser console for JavaScript errors
4. Ensure proper CORS configuration in the planning service

#### Goals Not Creating
**Problem**: Goal creation form submission fails
**Solutions**:
1. Verify all required fields are filled (description is mandatory)
2. Check success criteria have valid metrics and targets
3. Ensure constraints have proper limit values and operators
4. Check network connectivity to the planning service API

#### Real-time Monitoring Not Working
**Problem**: Goal status doesn't update automatically
**Solutions**:
1. Check that monitoring is enabled (green indicator should be visible)
2. Verify the goal is in an active state (planning, executing)
3. Check browser console for polling errors
4. Ensure the planning service is responding to status requests

#### Task Dependency Graph Not Displaying
**Problem**: Dependency graph shows empty or broken visualization
**Solutions**:
1. Verify tasks have been generated (plan must exist)
2. Check that task dependencies are properly defined
3. Ensure SVG rendering is supported in the browser
4. Check for JavaScript errors in the browser console

#### Cost Insights Showing Incorrect Data
**Problem**: Cost analysis displays zero or incorrect values
**Solutions**:
1. Verify goals have completed execution to generate cost data
2. Check that the planning service is tracking execution costs
3. Ensure proper integration with the cost tracking system
4. Verify time range settings for cost analysis

### Performance Optimization

#### Slow Dashboard Loading
**Optimizations**:
1. Enable browser caching for static assets
2. Implement service worker for offline functionality
3. Use CDN for faster asset delivery
4. Optimize API response sizes

#### High Memory Usage
**Solutions**:
1. Limit the number of goals displayed per page
2. Implement virtual scrolling for large lists
3. Clear old monitoring data periodically
4. Optimize React component re-rendering

### Development Setup

#### Local Development
```bash
# Start the frontend development server
cd k8s/frontend
npm install
npm run dev

# Access the dashboard at http://localhost:5173/
```

#### Building for Production
```bash
# Build optimized production bundle
npm run build

# Preview production build
npm run preview
```

### API Integration Testing

#### Test Planning Service Connectivity
```bash
# Test goal creation
curl -X POST https://scale-llm.com/v1/goals \
  -H "Content-Type: application/json" \
  -H "X-User-ID: test-user" \
  -d '{
    "description": "Test goal for connectivity",
    "priority": 5
  }'

# Test goal listing
curl https://scale-llm.com/v1/goals \
  -H "X-User-ID: test-user"
```

#### Monitor API Performance
```bash
# Check API response times
curl -w "@curl-format.txt" -o /dev/null -s https://scale-llm.com/v1/goals

# Where curl-format.txt contains:
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n
```

For additional support, refer to the planning service logs and dashboard API documentation.