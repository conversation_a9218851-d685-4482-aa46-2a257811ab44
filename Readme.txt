
#Project Architecture Overview: AI Cost-Performance Optimizer with Autonomous Planning Engine
This system is designed to intelligently route AI inference requests to optimal backends AND enable autonomous execution of complex multi-step workflows through goal-driven planning. The platform operates in two modes:

1. DIRECT LLM OPTIMIZATION: Intelligent routing of individual LLM requests based on dynamic policies, real-time performance metrics, and cost considerations
2. AUTONOMOUS WORKFLOW EXECUTION: Goal-driven planning engine that decomposes complex objectives into executable task workflows with intelligent orchestration

The system provides comprehensive logging, analytics, and autonomous task execution capabilities for both optimization and planning workflows.

1. Enhanced System Architecture Diagram
+-----------------------+
|                       |
|        User/Client    |
|                       |
+-----------+-----------+
            |
            | 1. HTTP(S) Requests:
            |    - Direct LLM: /v1/chat/completions
            |    - Planning: /v1/goals, /v1/goals/{id}/plan, /v1/goals/{id}/execute
            |    (Includes X-Data-Sensitivity, X-User-ID headers)
            V
+-----------+-----------+
|                       |
|     Proxy Gateway     |
|   (Go Lang Service)   |
|   [Enhanced with      |
|    Planning Routes]   |
+-----------+-----------+
    |       |       |
    |       |       | 2. HTTP(S) Requests:
    |       |       |    - LLM Backend Requests
    |       |       |    - Planning Service Requests
    |       |       +-------------------------------------------------+
    |       |                                                         |
    |       | 3. Redis GET/SET (Model Profiles, Policies, Live Latency,
    |       |                   Planning State, Execution Context)    |
    |       | 4. Redis Pub/Sub (Policy/Profile Updates)               |
    |       |                                                         |
    |       | 5. Kafka Producer (Inference + Planning Logs)           |
    |       |                                                         |
    |       | 6. HTTP(S) Proxy Request (to Dashboard API)             |
    |       |                                                         |
    V       V                                                         V
+---+-------+---+       +-----------------------+       +-----------------------+
|               |       |                       |       |                       |
|    Redis      |<----->|    Policy Manager     |       |   Planning Service    |
| (Cache, Metrics,      | (Go Lang Service)     |       |  (Go Lang Service)    |
|   Pub/Sub,    |       +-----------+-----------+       |  [NEW COMPONENT]     |
| Planning State)|                  |                    +-----------+-----------+
+---------------+                   |                               ^
                                    |                               |
                                    |                               | Planning API Calls
                                    |                               |
                            +-----------------------+       +-----------------------+
                            |                       |       |                       |
                            |   Mock Backend GPU 1  |       |   Mock Backend GPU 2  |
                            | (Simulated AI Service)|       | (Simulated AI Service)|
                            +-----------+-----------+       +-----------+-----------+
                                        ^                               ^
                                    | 7. Firestore API (Policies, Model Profiles)
                                    V                               |
                            +-----------------+                     |
                            |                 |                     |
                            |    Firestore    |                     |
                            | (Source of Truth)|                     |
                            +-----------------+                     |
                                                                    | 8. HTTP(S) Responses (LLM + Planning)
+-----------------------+                                           |
|                       |                                           |
|     Kafka             |                                           |
| (Message Broker)      |                                           |
| [Enhanced with        |                                           |
|  Planning Events]     |                                           |
+-----------+-----------+                                           |
            ^                                                       |
            | 9. Kafka Consumer (Inference + Planning Logs)         |
            |                                                       |
+-----------+-----------+                                           |
|                       |                                           |
|    Data Processor     |                                           |
| (Go Lang Service)     |                                           |
| [Enhanced with        |                                           |
|  Planning Analytics]  |                                           |
+-----------+-----------+                                           |
    |       |       |                                               |
    |       |       | 10. Prometheus HTTP API (CPU/Memory Metrics)  |
    |       |       +-------------------------------------------------+
    |       |                                                         |
    |       | 11. ClickHouse TCP/HTTP (LLM + Planning Analytics)      |
    |       |                                                         |
    V       V                                                         V
+-----------+-----------+       +-----------------------+       +-----------------------+
|                       |       |                       |       |                       |
|    ClickHouse         |       |      Prometheus       |       |     AI Optimizer      |
| (Analytics Database)  |       | (Metrics Scraper)     |       |   (Go Lang Service)   |
| [Enhanced with        |       +-----------+-----------+       | [Enhanced with        |
|  Planning Analytics]  |                   ^                   |  Planning Awareness]  |
+-----------------------+                   |                   +-----------+-----------+
                                            ^
                                            | 12. Scrapes Metrics (e.g., cAdvisor)
                                            |
                                        ^   ^
                                        |   | (Reads metrics for LLM + Planning optimization)
                                        |   |
                                +-------+---+-------+
                                |                       |
                                |    Dashboard API      |
                                |  (Go Lang Service)    |
                                | [Enhanced with        |
                                |  Planning Metrics]    |
                                +-----------+-----------+
                                        ^   ^
                                        |   | (Queries LLM + Planning data)
                                        |   |
                                +-------+---+-------+
                                |                       |
                                |       React App       |
                                |     (Frontend UI)     |
                                | [Enhanced with        |
                                |  Planning Dashboard]  |
                                | (via API Gateway/Nginx)|
                                +-----------------------+

2. Component Descriptions and Functionality
a. User/Client
Functionality: Initiates both direct AI inference requests AND autonomous goal-driven workflows.

Direct LLM Mode: Traditional text generation, image analysis, etc.
Autonomous Planning Mode: High-level goal submission for multi-step workflow execution.

Communication:
- Direct LLM: HTTP(S) POST to /v1/chat/completions
- Planning: HTTP(S) POST to /v1/goals, /v1/goals/{id}/plan, /v1/goals/{id}/execute
- Headers: X-Data-Sensitivity, X-User-ID

b. Proxy Gateway (Go Lang Service) [ENHANCED]
Functionality: The central routing component for BOTH direct LLM requests AND planning workflows.

Direct LLM Mode:
- Receives individual inference requests
- Uses intelligent algorithm for backend selection based on policies, model profiles, and real-time metrics
- Proxies requests to selected LLM backends

Planning Mode:
- Routes planning requests to Planning Service
- Handles goal creation, plan generation, and execution monitoring
- Provides unified API for autonomous workflows

Backend Selection (for LLM requests): Based on:
- Request attributes (X-Data-Sensitivity, X-User-ID headers)
- Dynamic Policies and Model Profiles
- Real-time Live Latency from Redis
- Planning-aware optimization for task-driven requests

Captures detailed logs for both LLM requests and planning activities.

Communication:

Receives: HTTP(S) requests from User/Client.

Sends: HTTP(S) requests to Mock Backend GPU 1/2.

Sends: HTTP(S) requests to the Dashboard API (for dashboard data requests originating from the React App, proxied through Nginx).

Communicates with Redis:

GET operations to fetch cached Model Profiles and Policies on startup.

GET operations to fetch Live Latency metrics.

SUBSCRIBE to Redis Pub/Sub channels (policy_updates, model_profile_updates) for real-time cache invalidation/updates from Policy Manager.

Communicates with Kafka: Acts as a Kafka Producer, sending both InferenceLog and PlanningLog messages to respective topics.

b2. Planning Service (Go Lang Service) [NEW COMPONENT]
Functionality: Autonomous Task Decomposition & Planning Engine for goal-driven workflow execution.

Core Capabilities:
- Goal Parsing: Converts natural language objectives into structured goals with success criteria and constraints
- Task Decomposition: Breaks down complex goals into executable tasks using LLM-assisted planning and templates
- Execution Orchestration: Manages task dependencies, parallel execution, and resource allocation
- State Management: Maintains persistent execution context across multi-step workflows
- Progress Monitoring: Provides real-time tracking of goal execution and task completion
- Adaptive Planning: Supports re-planning and failure recovery during execution

Task Execution Framework:
- LLM Tasks: Route through AI Optimizer for cost-optimized language model calls
- Data Tasks: Execute database queries and data retrieval operations
- API Tasks: Make HTTP requests to external services
- Analysis Tasks: Perform data analysis and statistical operations
- Validation Tasks: Verify results against success criteria
- Aggregation Tasks: Combine results from multiple tasks

Communication:
- Receives: HTTP(S) requests from Proxy Gateway for planning operations
- Sends: HTTP(S) requests to AI Optimizer for LLM routing during task execution
- Communicates with Redis: Stores execution context, goal state, and task results
- Communicates with Kafka: Publishes planning events and execution logs

c. Mock Backend GPU 1 & 2 (Simulated AI Services)
Functionality: Simulate actual AI model inference endpoints for both direct LLM requests and planning task execution.

Communication:

Receives: HTTP(S) requests from Proxy Gateway (direct LLM) and Planning Service (task execution).

Sends: HTTP(S) responses back to requesting services.

Monitored by: Prometheus (via cAdvisor metrics).

d. Policy Manager (Go Lang Service)
Functionality: Manages the "source of truth" for dynamic routing policies and model profiles.

Watches Firestore for any changes (additions, modifications, deletions) to policies and model profiles.

Propagates these changes to Redis by setting/deleting individual keys.

Publishes these changes to Redis Pub/Sub channels for real-time updates.

Exposes a basic HTTP API for manual CRUD operations (though typically automated via Firestore).

Communication:

Communicates with Firestore: Uses the Firestore API to set up snapshot listeners for policies and model_profiles collections.

Communicates with Redis:

SET operations to store Policy and ModelProfile objects as individual keys (e.g., policy:<ID>, model_profile:<ID>).

DEL operations to remove keys when documents are deleted in Firestore.

PUBLISH operations to policy_updates and model_profile_updates Pub/Sub channels.

e. Firestore (Google Cloud Service)
Functionality: A NoSQL cloud database that serves as the authoritative source of truth for Policies and Model Profiles.

Communication:

Receives: API calls from Policy Manager (for snapshot listeners, and potentially CRUD operations).

f. Redis (In-Memory Data Store) [ENHANCED]
Functionality: Serves multiple critical roles for both LLM optimization and planning workflows:

Cache: Stores Policies and Model Profiles for low-latency routing decisions.

Real-time Metrics Store: Stores Live Latency data for each backend.

Planning State Store: Maintains execution context, goal state, and task results for autonomous workflows.

Pub/Sub Broker: Facilitates real-time communication for cache synchronization and planning updates.

Communication:

Receives: SET/DEL/PUBLISH commands from Policy Manager, Data Processor, and Planning Service.

Sends: GET responses and Pub/Sub messages to all services including Planning Service.

g. Kafka (Message Broker) [ENHANCED]
Functionality: Provides highly scalable message queuing for both Inference Logs and Planning Events. Ensures reliable delivery and processing of all system events.

Communication:

Receives: Messages from Proxy Gateway (LLM logs) and Planning Service (planning events).

Sends: Messages to Data Processor for both LLM and planning analytics.

h. Data Processor (Go Lang Service) [ENHANCED]
Functionality: Processes both inference logs and planning events from Kafka, providing comprehensive analytics.

LLM Analytics:
- Consumes Inference Logs from Kafka
- Queries Prometheus for CPU/Memory usage during inference
- Calculates total cost (base cost + resource cost)
- Updates Live Latency in Redis

Planning Analytics:
- Consumes Planning Events from Kafka (goal creation, task execution, workflow completion)
- Tracks goal success rates, task performance, and workflow costs
- Calculates planning effectiveness metrics

Inserts enriched data into ClickHouse for both LLM and planning analytics.

Communication:

Receives: Messages from Kafka (Consumer).

Sends: HTTP GET requests to Prometheus API.

Communicates with Redis: SET operations to update Live Latency keys.

Communicates with ClickHouse: TCP/HTTP connections to insert processed logs.

i. ClickHouse (Analytics Database) [ENHANCED]
Functionality: High-performance columnar database storing both LLM inference logs and planning analytics for comprehensive system insights.

Data Storage:
- LLM inference logs for traditional optimization analytics
- Planning events: goal creation, task execution, workflow completion
- Goal success rates, task performance metrics, and workflow costs
- Historical data for both LLM routing and planning optimization

Communication:

Receives: TCP/HTTP connections from Data Processor for both LLM and planning data.

Sends: Query results to AI Optimizer and Dashboard API for unified analytics.

j. Prometheus (Monitoring System)
Functionality: A time-series database and monitoring system. It continuously scrapes metrics from configured targets (e.g., Kubernetes pods running Mock Backend GPU services via cAdvisor metrics).

Communication:

Scrapes: HTTP endpoints of Mock Backend GPU 1/2 (and other Kubernetes components) to collect metrics.

Serves: HTTP API queries from Data Processor.

k. AI Optimizer (Go Lang Service) [ENHANCED] - Intelligent Routing & Planning Component
Functionality: Enhanced analytics and routing engine supporting both LLM optimization and planning-aware decisions.

LLM Optimization:
- Periodically runs optimization cycles for backend performance analysis
- Fetches historical inference logs from ClickHouse
- Analyzes backend performance metrics and real-time latency
- Makes routing decisions for individual LLM requests

Planning Integration:
- Provides cost-optimized LLM routing for planning task execution
- Considers workflow-level optimization when routing planning-driven requests
- Supports planning-specific routing policies and preferences
- Integrates with Planning Service for task-aware routing decisions

Exposes /route endpoint for both Proxy Gateway and Planning Service routing requests.

Communication:

Queries ClickHouse: Fetches historical data for its internal analysis.

Queries Redis: Fetches live data for its internal analysis.

l. Dashboard API (Go Lang Service) [ENHANCED] - Unified Frontend Backend
Functionality: Enhanced backend serving both LLM optimization and planning analytics to the React dashboard.

API Endpoints:
- Traditional: /api/inference_summary, /api/time_series_summary for LLM analytics
- Planning: /api/planning_metrics, /api/goal_analytics, /api/workflow_performance for planning insights

Data Sources:
- Queries ClickHouse for both LLM and planning historical data
- Queries Redis for real-time metrics including planning execution status

Communication:

Receives: HTTP requests for both LLM and planning dashboard data.

Queries ClickHouse: Historical data for LLM optimization and planning analytics.

Queries Redis: Live metrics for both LLM routing and planning execution.

m. React App (Frontend UI) [ENHANCED]
Functionality: Unified user interface supporting both LLM optimization and autonomous planning workflows.

LLM Analytics Features:
- View aggregated inference summaries and trends
- Visualize metrics (requests, latency, tokens, cost)
- Apply filters and manage routing policies/model profiles

Planning Dashboard Features:
- Goal creation and management interface
- Real-time workflow execution monitoring
- Task dependency visualization and progress tracking
- Planning effectiveness metrics and cost analysis
- Workflow templates and success rate analytics

Communication: Makes API calls for both LLM optimization and planning data through the unified API Gateway.

n. Kafka Topic Creator (Utility) [ENHANCED]
Functionality: Enhanced utility ensuring both LLM and planning-related Kafka topics are created.

Topics Created:
- inference-logs: For traditional LLM request logging
- planning-events: For goal creation, task execution, and workflow completion events
- planning-metrics: For planning performance and analytics data

Communication:

Communicates with Kafka: Creates topics and sends test messages for both LLM and planning workflows.




How the Enhanced Routing Works:

The system now supports TWO types of routing:

1. DIRECT LLM ROUTING: Traditional model routing for individual requests
2. PLANNING-AWARE ROUTING: Intelligent routing for autonomous workflow tasks

DIRECT LLM ROUTING:
The proxy-gateway determines whether to route requests to mock backends or real external models based on the BackendURL defined within the ModelProfile selected for a given request.

PLANNING-AWARE ROUTING:
The Planning Service routes task-specific LLM calls through the AI Optimizer, which considers:
- Task type and complexity
- Workflow-level cost optimization
- Planning-specific routing policies
- Resource allocation across the entire goal execution

Here's a breakdown of the routing logic in proxy-gateway/main.go and how this distinction is made:

The selectBackend Function: The Brain of Routing
The selectBackend function is where the core routing decision is made. It follows a specific hierarchy:

Policy Evaluation (Highest Priority):

The proxy-gateway first iterates through all configured policies, sorted by their Priority (higher priority evaluated first).
It checks if any ROUTE policy's Criteria match the incoming request (e.g., model_requested, client_ip, data_sensitivity, prompt_length).
If a ROUTE policy matches, the policy.BackendID is used to look up a corresponding ModelProfile from its in-memory cache.
Fallback 1: Requested Model Name or Alias:

If no explicit ROUTE policy matches, the proxy then attempts to find a ModelProfile whose Name or Aliases directly match the model specified in the incoming LLM request's body (e.g., "gpt-4o-mini", "gemini-flash").
Fallback 2: AI Optimizer's Optimal Backend:

If neither policies nor a direct model name/alias match, the proxy-gateway checks Redis for the REDIS_OPTIMAL_BACKEND_KEY ("routing:optimal_backend_id"). The ai-optimizer service is responsible for publishing the ID of the currently "optimal" backend to this key.
If an optimal backend ID is found, the proxy retrieves its ModelProfile.
Fallback 3: First Available Model Profile:

As a last resort, if none of the above criteria yield a backend, the proxy-gateway will simply pick the first ModelProfile it finds in its cache. This serves as a safety net to ensure requests are always routed if possible.
How BackendURL Dictates the Target
Once a ModelProfile is selected by any of the above mechanisms, the SendRequestToBackend function is called. This function uses the BackendURL field from the chosen ModelProfile to construct the actual HTTP request that is sent to the LLM.

Routing to Mock Backends:
Your mock backends (e.g., mock-backend-gpu1, mock-google, mock-anthropic, mock-openai) are typically deployed within your Kubernetes cluster. Their ModelProfiles will have BackendURLs pointing to their internal Kubernetes service names and ports.

Example: http://mock-backend-gpu1:5001/predict or http://mock-openai:5003/v1/chat/completions
Routing to Real External Models:
The ModelProfiles for real OpenAI, Google, or Anthropic models will have BackendURLs pointing to their public internet endpoints.

Example: https://api.openai.com/v1/chat/completions
Example: https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent
Example: https://api.anthropic.com/v1/messages
In Summary:
The routing decision is a cascade:

The proxy-gateway figures out which ModelProfile to use based on policies, requested model name, or the AI optimizer's recommendation.
Once a ModelProfile is chosen, the proxy-gateway simply uses the BackendURL specified within that ModelProfile to send the request.
So, if a ModelProfile's BackendURL is set to an internal Kubernetes service address, the request goes to your mock. If it's set to an external API endpoint, it goes to the real provider. The choice between mock and real is entirely driven by how you define and manage the BackendURLs in your ModelProfiles (which are stored in Redis).






Policy Form Fields Explained
This section details the fields used when defining or editing a routing policy.

Name

Purpose: A human-readable identifier for the policy. This should be concise and descriptive.

Example: "High-Priority GPT-4 Requests," "Cost-Optimized Llama-2 Routing," "Block Malicious IPs."

Description

Purpose: Provides more detailed information about the policy's intent, the specific scenarios it covers, or any caveats. This helps other team members understand its function without diving into the code.

Example: "Routes all production GPT-4 large model requests to dedicated GPU backends to minimize latency," or "Denies requests from known problematic IP ranges identified by security."

Action

Purpose: Defines what the router should do when an incoming inference request matches the policy's Criteria. This is the core outcome of the policy.

Possible Values:

ROUTE_TO_BACKEND: Send the request to the Backend ID specified in this policy.

REJECT: Block the request entirely (e.g., return a 403 Forbidden or 429 Too Many Requests).

OPTIMIZE_COST: Dynamically select the cheapest available backend from a pool of Model Profiles that meet the request's needs.

OPTIMIZE_LATENCY: Dynamically select the fastest available backend.

LOG_ONLY: Process the request as usual but specifically log its details for analysis without altering routing.

Example: "ROUTE_TO_BACKEND," "REJECT," "OPTIMIZE_COST."

Backend ID

Purpose: If the Action is set to ROUTE_TO_BACKEND, this field specifies the exact ID of the target model backend where the inference request should be sent. This ID would typically correspond to the id of one of your Model Profiles.

Example: "mock-backend-gpu1," "azure-openai-us-east," "on-prem-cpu-cluster."

Priority

Purpose: Determines the order in which policies are evaluated. When multiple policies could potentially match an incoming request, the policy with the higher priority (typically a lower number, like 1 being highest priority) is applied first.

Example: 1 (highest), 10, 100 (lowest). You'd want a "block malicious IPs" policy to have a higher priority than a general routing policy.

Rules (JSON String)

Purpose: This field is for more complex or custom routing logic that might not be easily expressed with simple key-value Criteria. By storing it as a JSON string, your backend can parse and interpret it for advanced scenarios.

Example: You might store a complex conditional expression, a weighted routing configuration, or parameters for A/B testing different backends.

Example Value: {"min_batch_size": 2, "max_concurrency": 5} or {"custom_function": "evaluate_user_tier"}.

Criteria (Repeating Group: Field, Operator, Value)

Purpose: These define the conditions that an incoming inference request must meet for the policy to be applied. A policy can have multiple criteria, which are usually evaluated with an "AND" logic (all criteria must be true).

Field: The name of the attribute or property of the incoming request you want to evaluate.

Example: "user_id," "model_name," "input_token_count," "origin_ip."

Operator: How you want to compare the Field to the Value.

Example: "=", "!=", ">", "<", "CONTAINS", "STARTS_WITH."

Value: The specific value you are comparing the Field against.

Example: "premium-user," "gpt-4-turbo," "1000," "***********."

Remove (Button for Criteria)

Purpose: Allows the user to remove a specific criterion from the policy.

This architecture provides a robust, scalable, and observable system for dynamic AI inference routing and performance analytics.
